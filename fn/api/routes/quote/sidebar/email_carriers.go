package quoteprivate

import (
	"context"
	"fmt"
	"net/http"
	"slices"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/integrations/email/frontclient"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/api/env"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	EmailCarriersBody struct {
		CarrierEmails               []string                      `json:"carrierEmails" validate:"dive,email"`
		Carriers                    []models.LocationWithDistance `json:"carriers"`
		Subject                     string                        `json:"subject" validate:"required"`
		EmailBody                   string                        `json:"emailBody" validate:"min=20"`
		From                        string                        `json:"from"`
		CCEmails                    []string                      `json:"ccEmails"`
		SelectedExistingAttachments []models.Attachment           `json:"selectedExistingAttachments"`
		NewAttachments              []NewAttachment               `json:"newAttachments"`
		AppliedRequest              GetQuoteRequestResponse       `json:"actualRequest"`
		CarrierGroupID              *uint                         `json:"carrierGroupId,omitempty"`
	}

	NewAttachment struct {
		Data     string `json:"data"` // Base64 encoded file data
		FileName string `json:"fileName"`
		MimeType string `json:"mimeType"`
	}

	EmailCarriersResponse struct {
		// If there was no suggestion and one was generated, return the parent quote request ID
		QuoteRequestID uint                   `json:"quoteRequestID"`
		Message        string                 `json:"message,omitempty"`
		CountSuccess   int                    `json:"countSuccess"`
		Results        map[string]EmailResult `json:"results"`
	}

	EmailResult struct {
		CarrierName string   `json:"carrierName"`
		Recipients  []string `json:"recipients"`
		ExternalID  string   `json:"externalID"`
		ThreadID    string   `json:"threadID"`
		Success     bool     `json:"success"`
		Duplicate   bool     `json:"duplicate"`
	}

	GenEmailAndCarrier struct {
		GenEmail        models.GeneratedEmail
		CarrierLocation models.TMSLocation
	}
)

func EmailCarriersForQuotes(c *fiber.Ctx) error {
	var body EmailCarriersBody
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	// Backwards-compatibility for old FE
	if len(body.Carriers) == 0 {
		body.Carriers = body.AppliedRequest.Carriers
	}

	if helpers.IsBlank(body.From) {
		body.From = body.AppliedRequest.From
	}

	if len(body.CarrierEmails) == 0 {
		return c.Status(http.StatusBadRequest).
			JSON(EmailCarriersResponse{Message: "Please select at least 1 carrier"})
	}

	// FE textarea sends plaintext, so we need to convert it to HTML
	body.EmailBody = strings.ReplaceAll(body.EmailBody, "\n", "<br/>")

	triggeredBy, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}
	sender := triggeredBy

	// NOTE: DO NOT email actual carriers in dev. Replace with your test emails
	// If you want to test the email to carrier name mapping, update records in your local DB
	// This check verifies devs email only internal accounts
	if perms.IsInternalEmail(ctx, triggeredBy.EmailAddress) {
		if slices.ContainsFunc(body.CarrierEmails, func(email string) bool {
			return !perms.IsInternalEmail(ctx, email)
		}) {
			return c.Status(http.StatusBadRequest).JSON(EmailCarriersResponse{
				Message: "Drumkit users cannot send to production carriers",
			})
		}
	}

	var quoteReq models.QuoteRequest
	// Request may not be in DB yet if AI failed to make a suggestion
	if body.AppliedRequest.ID != 0 {
		err = rds.GetByID(ctx, body.AppliedRequest.ID, &quoteReq)
		if err != nil {
			log.WarnNoSentry(ctx, "error fetching quote request from DB", zap.Error(err))
		}
	}

	var resp EmailCarriersResponse
	var sentCarrierEmails []GenEmailAndCarrier

	switch {
	case sender.EmailProvider == models.FrontEmailProvider:
		service, err := rds.GetServiceByID(ctx, sender.ServiceID)
		if err != nil {
			log.Error(ctx, "error getting service for Front user", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		frontClient, err := frontclient.New()
		if err != nil {
			log.Error(ctx, "error creating Front client", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		resp.Results, sentCarrierEmails = emailCarriersFront(
			ctx,
			triggeredBy,
			body,
			quoteReq,
			sender,
			service,
			frontClient,
		)

	case sender.OutlookClientState != "":
		// TODO: Support sending from aliases and delegated inboxes when requested by customer
		msClient, err := msclient.New(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &sender)
		if err != nil {
			log.Error(ctx, "error creating Outlook client", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		resp.Results, sentCarrierEmails = emailCarriersOutlook(ctx, triggeredBy, body, quoteReq, sender, msClient)

	default:
		gmailClient, err := gmailclient.New(ctx, env.Vars.GoogleClientID, env.Vars.GoogleClientSecret, &sender)
		if err != nil {
			log.Error(ctx, "error creating Gmail client", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		// Users can send from aliases; check that alias exists
		// TODO: Support sending from delegated inboxes when requested by customer
		if body.From != "" && body.From != triggeredBy.EmailAddress {
			aliases, err := gmailClient.GetAliases(ctx)
			if err != nil {
				log.Error(ctx, "error getting Gmail aliases", zap.Error(err))
				return c.
					Status(http.StatusInternalServerError).
					JSON(EmailCarriersResponse{
						Message: "Unable to fetch alias at the moment. Please try again later or contact support"})
			}

			if !slices.Contains(aliases, body.From) {
				log.WarnNoSentry(
					ctx,
					"body.From is not an alias of the user",
					zap.String("body.From", body.From),
				)

				return c.Status(http.StatusBadRequest).
					JSON(EmailCarriersResponse{
						Message: "You are not authorized to send emails from this alias.",
					})
			}
		}

		resp.Results, sentCarrierEmails = emailCarriersGmail(
			ctx,
			triggeredBy,
			body.From,
			body,
			quoteReq,
			gmailClient,
		)
	}

	var sentEmails []models.GeneratedEmail
	var initCarrierQuotes []models.CarrierQuote

	for _, sentCarrierEmail := range sentCarrierEmails {
		sentEmails = append(sentEmails, sentCarrierEmail.GenEmail)

		carrierQuote := models.CarrierQuote{
			ThreadID:          sentCarrierEmail.GenEmail.ThreadID,
			Status:            models.RequestedCarrierQuoteStatus,
			ServiceID:         sentCarrierEmail.GenEmail.ServiceID,
			CarrierLocationID: sentCarrierEmail.CarrierLocation.ID,
		}

		if body.CarrierGroupID != nil {
			carrierQuote.SelectedCarrierGroupID = body.CarrierGroupID
		}

		initCarrierQuotes = append(initCarrierQuotes, carrierQuote)
	}

	// Initialize QuoteRequest if none exists yet
	if quoteReq.ID == 0 {
		rfcMessageID := ""
		if body.AppliedRequest.EmailID != 0 {
			email, err := emailDB.GetByID(ctx, body.AppliedRequest.EmailID)
			if err != nil {
				log.Warn(ctx, "error getting email by ID", zap.Error(err))
			}
			rfcMessageID = email.RFCMessageID
		}

		quoteReq.UserID = triggeredBy.ID
		quoteReq.EmailID = body.AppliedRequest.EmailID
		quoteReq.ThreadID = body.AppliedRequest.ThreadID
		quoteReq.RFCMessageID = rfcMessageID
		quoteReq.ServiceID = triggeredBy.ServiceID
		quoteReq.Status = models.InFlight
	}
	quoteReq.CarrierEmails = sentEmails
	quoteReq.CarrierQuotes = initCarrierQuotes
	quoteReq.AppliedRequest = models.QuoteLoadInfo{
		TransportType:    body.AppliedRequest.TransportType,
		Commodity:        body.AppliedRequest.Commodity,
		WeightLbs:        body.AppliedRequest.WeightLbs,
		PickupLocation:   body.AppliedRequest.PickupLocation,
		PickupDate:       body.AppliedRequest.PickupDate,
		DeliveryLocation: body.AppliedRequest.DeliveryLocation,
		DeliveryDate:     body.AppliedRequest.DeliveryDate,
		// These two can't be corrected by user in frontend rn
		Distance: quoteReq.SuggestedRequest.Distance,
		Pallets:  quoteReq.SuggestedRequest.Pallets,
	}

	// If no existing request found, create a new one. If one exists, update it with the
	// finalized load info and emails sent to carrier.
	if err = quoteRequestDB.UpsertQuoteRequestAndAssociations(ctx, &quoteReq); err != nil {
		log.Error(
			ctx,
			`error updating quote request and sent emails,
			check if issue persists and/or manually adjust DB`,
			zap.Error(err),
		)
	}

	// If emails were all or partially unsuccessful, return 500
	var failedCarriersString string
	for carrierName, e := range resp.Results {
		if e.Success {
			resp.CountSuccess++
		} else {
			failedCarriersString += carrierName + ", "
		}
	}

	if resp.CountSuccess == 0 {
		resp.Message = "Failed to email carrier(s)"
	} else if resp.CountSuccess < len(resp.Results) {
		resp.Message = fmt.Sprintf("Partially succeeded in sending %d carrier email(s)", resp.CountSuccess)
	}

	if resp.Message != "" {
		log.Error(ctx, resp.Message, zap.Any("results", resp.Results))

		return c.Status(http.StatusInternalServerError).JSON(resp)
	}

	return c.Status(http.StatusOK).JSON(resp)
}

// buildCarrierEmailMapping creates a mapping between carrier emails and their names
// and returns a map of carrier names to their email addresses
//
// map[*models.LocationWithDistance][]string: key is the carrier location, value is a list of selected email addresses.
func buildCarrierEmailMapping(
	carriers []models.LocationWithDistance,
	selectedEmails []string,
) map[*models.LocationWithDistance][]string {

	carrierEmails := make(map[*models.LocationWithDistance][]string)

	// Create a set of selected emails for faster lookup
	selectedEmailSet := make(map[string]bool)
	for _, email := range selectedEmails {
		selectedEmailSet[email] = true
	}

	for i, carrierLoc := range carriers {
		// Handle carriers with multiple emails
		var selectedCarrierEmails []string
		if len(carrierLoc.Emails) > 0 {
			// Only include emails that were selected
			for _, email := range carrierLoc.Emails {
				if selectedEmailSet[email] {
					selectedCarrierEmails = append(selectedCarrierEmails, email)
				}
			}
		} else if carrierLoc.Email != "" && selectedEmailSet[carrierLoc.Email] {
			// Fallback to single email field if it was selected
			selectedCarrierEmails = []string{carrierLoc.Email}
		}

		// Only add carrier if they have selected emails
		if len(selectedCarrierEmails) > 0 {
			carrierEmails[&carriers[i]] = selectedCarrierEmails
		}
	}

	return carrierEmails
}

// emailCarriersOutlook emails carriers using the Outlook client.
// `triggeredByUser` is the Drumkit user that triggered sending the msg from that account (e.g. <EMAIL>).
// The `sender` is the Drumkit account that actually sent the email (e.g. <EMAIL>).
// They may be different if the user is sending from a delegated inbox.
//
// map[string]EmailResult: key is the carrier name, value is the result of the email sending operation.
// []models.GeneratedEmail: list of generated emails that were successfully sent.
// TODO: Handle attachments and sending from alias
// FIXME: ThreadID is not immutable, different in email creation vs. email received for same thread. Grrrr
func emailCarriersOutlook(
	ctx context.Context,
	triggeredByUser models.User,
	body EmailCarriersBody,
	quoteReq models.QuoteRequest,
	sender models.User,
	client msclient.Client,
) (map[string]EmailResult, []GenEmailAndCarrier) {

	carrierEmails := buildCarrierEmailMapping(body.Carriers, body.CarrierEmails)
	result := make(map[string]EmailResult)
	sentEmails := []GenEmailAndCarrier{}

	// Group emails by carrier
	for carrierLoc, emails := range carrierEmails {
		// Skip if no emails for carrier
		if len(emails) == 0 {
			continue
		}

		// Check for duplicates
		isDupe := func(e models.GeneratedEmail) bool {
			for _, email := range emails {
				if slices.Contains(e.Recipients, email) {
					return true
				}
			}
			return false
		}

		// Location name can be quite long like "Freight Force - NEW YORK, NY" so we prefer the parent carrier name,
		// if available (e.g. "Freight Force")
		carrierName := helpers.Or(carrierLoc.TMSCarrier.Name, carrierLoc.Name)

		if slices.ContainsFunc(quoteReq.CarrierEmails, isDupe) {
			// To test the same quote request in dev, uncomment conditional
			if env.Vars.AppEnv != "dev" {
				log.Infof(
					ctx,
					"skipping duplicate email to carrier %s for quote request %d",
					carrierName,
					quoteReq.ID,
				)

				result[carrierName] = EmailResult{
					CarrierName: carrierName,
					Recipients:  emails,
					Success:     false,
					Duplicate:   true,
				}

				continue
			}
		}

		subject := strings.ReplaceAll(body.Subject, "[Carrier Name]", carrierName)

		msg := msclient.Message{
			CreatedDateTime:      time.Now(),
			LastModifiedDateTime: time.Now(),
			// NOTE: Do not remove empty fields; MS throws an error
			HasAttachments:    len(body.SelectedExistingAttachments) > 0 || len(body.NewAttachments) > 0,
			InternetMessageID: "",
			WebLink:           "",
			Categories:        []string{},
			Subject:           subject,
			Body: &msclient.Body{
				ContentType: "html",
				Content:     body.EmailBody,
			},
			Sender: msclient.Sender{
				EmailAddress: msclient.EmailAddress{
					Address: client.GetAuthenticatedUser().GetEmailAddress(),
				},
			},
			From: msclient.From{
				EmailAddress: msclient.EmailAddress{
					Address: client.GetAuthenticatedUser().GetEmailAddress(),
				},
			},
			// cc, bcc, replyTo cannot be null
			CcRecipients: func() []msclient.RecipientCollection {
				ccRecipients := make([]msclient.RecipientCollection, len(body.CCEmails))
				for i, ccEmail := range body.CCEmails {
					ccRecipients[i] = msclient.RecipientCollection{
						EmailAddress: msclient.EmailAddress{Address: ccEmail},
					}
				}
				return ccRecipients
			}(),
			BccRecipients:           []msclient.RecipientCollection{},
			ReplyTo:                 []msclient.RecipientCollection{},
			Importance:              "normal",
			InferenceClassification: "focused",
			Flag:                    msclient.Flag{FlagStatus: "notFlagged"},
		}

		// FYI: Invalid email addresses just bounce, no error messages thrown by API
		// Processor looks for and captures these bounces
		msg.ToRecipients = make([]msclient.RecipientCollection, len(emails))
		for i, email := range emails {
			msg.ToRecipients[i] = msclient.RecipientCollection{
				EmailAddress: msclient.EmailAddress{Address: email},
			}
		}

		// Must first draft email to get its immutable ID, then send it
		// https://learn.microsoft.com/en-us/graph/outlook-immutable-id#immutable-id-with-sending-mail
		if err := client.DraftMessage(ctx, &msg); err != nil {
			log.Error(ctx, "error drafting email", zap.String("carrier", carrierName), zap.Error(err))
			result[carrierName] = EmailResult{
				CarrierName: carrierName,
				Recipients:  emails,
				Success:     false,
			}
			continue
		}

		log.Debug(
			ctx,
			"drumkit-generated message ID",
			zap.String("newMsgID", msg.ID),
			zap.String("carrier", carrierName),
		)

		// Process existing attachments
		dbAttachments := body.SelectedExistingAttachments

		// Process new file attachments if any
		if len(body.NewAttachments) > 0 {
			for i, attachment := range body.NewAttachments {
				attachmentName := attachment.FileName

				newAttachment := models.Attachment{
					MessageExternalID:   fmt.Sprintf("quote-request-%d", quoteReq.ID),
					ExternalID:          fmt.Sprintf("uploaded-attachment-%d", i+1),
					MimeType:            attachment.MimeType,
					IsInline:            false,
					IsInSenderSignature: false,
					OriginalFileName:    attachmentName,
					TransformedFileName: s3backup.SanitizeFileName(attachmentName),
				}

				msAttachment := msclient.Attachment{
					ODataType:    "#microsoft.graph.fileAttachment",
					Name:         attachmentName,
					ContentType:  attachment.MimeType,
					ContentBytes: attachment.Data,
				}

				err := client.AddAttachment(
					ctx,
					msg.ID,
					&msAttachment,
				)

				if err != nil {
					log.Error(ctx, "Failed to add attachment to message",
						zap.Error(err),
						zap.String("carrier", carrierName),
						zap.Int("attachmentIndex", i),
					)
					continue
				}

				dbAttachments = append(dbAttachments, newAttachment)
			}
		}

		if err := client.SendMessage(ctx, &msg); err != nil {
			log.Error(ctx, "error sending email", zap.String("carrier", carrierName), zap.Error(err))
			result[carrierName] = EmailResult{
				CarrierName: carrierName,
				Recipients:  emails,
				Success:     false,
			}
			continue
		}

		dbEmail := msg.ToGeneratedEmailModel(&sender, &triggeredByUser)
		dbEmail.Recipients = emails
		dbEmail.Attachments = dbAttachments

		result[carrierName] = EmailResult{
			CarrierName: carrierName,
			Recipients:  emails,
			Success:     true,
			ExternalID:  msg.ID,
			ThreadID:    msg.ConversationID,
		}

		sentEmails = append(
			sentEmails,
			GenEmailAndCarrier{GenEmail: dbEmail, CarrierLocation: carrierLoc.TMSLocation},
		)
	}

	return result, sentEmails
}

// emailCarriersFront emails carriers using the Front client.
// `triggeredByUser` is the Drumkit user that triggered sending the msg from that account (e.g. <EMAIL>).
// The `sender` is the Drumkit account that actually sent the email (e.g. <EMAIL>).
// They may be different if the user is sending from a delegated inbox.
//
// map[string]EmailResult: key is the carrier name, value is the result of the email sending operation.
// []GenEmailAndCarrier: list of generated emails that were successfully sent.
func emailCarriersFront(
	ctx context.Context,
	triggeredByUser models.User,
	body EmailCarriersBody,
	quoteReq models.QuoteRequest,
	sender models.User,
	service models.Service,
	client frontclient.Client,
) (map[string]EmailResult, []GenEmailAndCarrier) {

	carrierEmails := buildCarrierEmailMapping(body.Carriers, body.CarrierEmails)
	result := make(map[string]EmailResult)
	sentEmails := []GenEmailAndCarrier{}

	// Group emails by carrier
	for carrierLoc, emails := range carrierEmails {
		// Skip if no emails for carrier
		if len(emails) == 0 {
			continue
		}

		// Check for duplicates
		isDupe := func(e models.GeneratedEmail) bool {
			for _, email := range emails {
				if slices.Contains(e.Recipients, email) {
					return true
				}
			}
			return false
		}

		carrierName := helpers.Or(carrierLoc.TMSCarrier.Name, carrierLoc.Name)

		if slices.ContainsFunc(quoteReq.CarrierEmails, isDupe) {
			// To test the same quote request in dev, uncomment conditional
			if env.Vars.AppEnv != "dev" {
				log.Infof(
					ctx,
					"skipping duplicate email to carrier %s for quote request %d",
					carrierName,
					quoteReq.ID,
				)

				result[carrierName] = EmailResult{
					CarrierName: carrierName,
					Recipients:  emails,
					Success:     false,
					Duplicate:   true,
				}

				continue
			}
		}

		subject := strings.ReplaceAll(body.Subject, "[Carrier Name]", carrierName)

		genEmail := models.GeneratedEmail{
			// Sender and triggeredBy user must belong to the same service
			ServiceID:         triggeredByUser.ServiceID,
			UserID:            sender.ID,
			Recipients:        emails,
			Subject:           subject,
			Body:              body.EmailBody,
			CC:                body.CCEmails,
			Sender:            helpers.Or(body.From, sender.EmailAddress),
			TriggeredByUserID: triggeredByUser.ID,
			Attachments:       body.SelectedExistingAttachments,
		}

		resMsg, err := client.SendMessage(ctx, &genEmail, service)
		if err != nil {
			log.Error(ctx, "error sending email", zap.String("carrier", carrierName), zap.Error(err))
			result[carrierName] = EmailResult{
				CarrierName: carrierName,
				Recipients:  emails,
				Success:     false,
			}

			continue
		}

		log.Debug(
			ctx,
			"drumkit-generated message ID",
			zap.String("newMsgID", resMsg.ID),
			zap.String("carrier", carrierName),
		)

		sentEmails = append(
			sentEmails,
			GenEmailAndCarrier{GenEmail: genEmail, CarrierLocation: carrierLoc.TMSLocation},
		)

		result[carrierName] = EmailResult{
			CarrierName: carrierName,
			Recipients:  emails,
			Success:     true,
			ExternalID:  resMsg.ID,
			ThreadID:    genEmail.ThreadID,
		}
	}

	return result, sentEmails
}

// emailCarriersGmail emails carriers using the Gmail client.
// `aliasEmail` is the alias the user wants to send from (e.g. <EMAIL> sends <NAME_EMAIL>).
// The alias can simply be the user's email address, which Gmail considers the primary email address.
//
// map[string]EmailResult: key is carrier name, value is the result of the email sending operation returned to the FE.
// []models.GeneratedEmail: list of generated emails that were successfully sent to store in RDS.
func emailCarriersGmail(
	ctx context.Context,
	triggeredBy models.User,
	aliasEmail string,
	body EmailCarriersBody,
	quoteReq models.QuoteRequest,
	client gmailclient.Client,
) (map[string]EmailResult, []GenEmailAndCarrier) {

	carrierEmails := buildCarrierEmailMapping(body.Carriers, body.CarrierEmails)
	result := make(map[string]EmailResult)
	sentEmails := []GenEmailAndCarrier{}

	signature, err := client.GetSignature(ctx, aliasEmail)
	if err != nil {
		log.Warn(
			ctx,
			"error getting signature, failing open",
			zap.Error(err),
			zap.String("aliasEmail", aliasEmail),
		)
	} else {
		log.Debug(ctx, "alias signature", zap.String("signature", signature))
	}

	// Group emails by carrier
	for carrierLoc, emails := range carrierEmails {
		// Skip if no emails for carrier
		if len(emails) == 0 {
			continue
		}

		// Check for duplicates
		isDupe := func(e models.GeneratedEmail) bool {
			for _, email := range emails {
				if slices.Contains(e.Recipients, email) {
					return true
				}
			}
			return false
		}

		carrierName := helpers.Or(carrierLoc.TMSCarrier.Name, carrierLoc.Name)

		if slices.ContainsFunc(quoteReq.CarrierEmails, isDupe) {
			// To test the same quote request in dev, uncomment conditional
			if env.Vars.AppEnv != "dev" {
				log.Infof(
					ctx,
					"skipping duplicate email to carrier %s for quote request %d",
					carrierName,
					quoteReq.ID,
				)

				result[carrierName] = EmailResult{
					CarrierName: carrierName,
					Recipients:  emails,
					Success:     false,
					Duplicate:   true,
				}

				continue
			}
		}

		subject := strings.ReplaceAll(body.Subject, "[Carrier Name]", carrierName)

		genEmail := models.GeneratedEmail{
			// Sender and triggeredBy user must belong to the same service
			ServiceID:         triggeredBy.ServiceID,
			UserID:            triggeredBy.ID,
			Recipients:        emails,
			Subject:           subject,
			Body:              body.EmailBody,
			CC:                body.CCEmails,
			Sender:            helpers.Or(aliasEmail, client.GetAuthenticatedUser().GetEmailAddress()),
			TriggeredByUserID: triggeredBy.ID,
			Attachments:       body.SelectedExistingAttachments,
		}

		// Prepare new attachments for the Gmail client
		var gmailNewAttachments []gmailclient.NewAttachment
		if len(body.NewAttachments) > 0 {
			log.Debug(ctx, "processing new attachments", zap.Int("count", len(body.NewAttachments)))

			gmailNewAttachments = make([]gmailclient.NewAttachment, 0, len(body.NewAttachments))
			for _, att := range body.NewAttachments {
				dataLen := len(att.Data)
				if dataLen == 0 {
					log.Warn(
						ctx,
						"skipping attachment with no data",
						zap.String("fileName", att.FileName),
					)

					continue
				}

				gmailNewAttachments = append(gmailNewAttachments, gmailclient.NewAttachment{
					FileName: att.FileName,
					MimeType: att.MimeType,
					Data:     att.Data,
				})
			}
		}

		resMsg, err := client.SendMessage(ctx, &genEmail, signature, gmailNewAttachments)
		if err != nil {
			log.Error(ctx, "error sending email", zap.String("carrier", carrierName), zap.Error(err))
			result[carrierName] = EmailResult{
				CarrierName: carrierName,
				Recipients:  emails,
				Success:     false,
			}

			continue
		}

		log.Debug(
			ctx,
			"drumkit-generated message ID",
			zap.String("newMsgID", resMsg.Id),
			zap.String("carrier", carrierName),
		)

		sentEmails = append(
			sentEmails,
			GenEmailAndCarrier{GenEmail: genEmail, CarrierLocation: carrierLoc.TMSLocation},
		)

		result[carrierName] = EmailResult{
			CarrierName: carrierName,
			Recipients:  emails,
			Success:     true,
			ExternalID:  resMsg.Id,
			ThreadID:    resMsg.ThreadId,
		}
	}

	return result, sentEmails
}
