package appt

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
)

func TestGetApptsForLoad(t *testing.T) {
	app := fiber.New()

	// Add middleware to set service ID in context
	app.Use(func(c *fiber.Ctx) error {
		c.Locals("beacon-service-id", uint(1))
		return c.Next()
	})

	app.Get("/appt", GetApptsForLoad)

	tests := []struct {
		name              string
		freightTrackingID string
		expectedStatus    int
	}{
		{
			name:              "missing freight tracking ID",
			freightTrackingID: "",
			expectedStatus:    http.StatusBadRequest,
		},
		{
			name:              "valid freight tracking ID",
			freightTrackingID: "TEST123",
			expectedStatus:    http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := "/appt"
			if tt.freightTrackingID != "" {
				url += "?freightTrackingId=" + tt.freightTrackingID
			}

			req := httptest.NewRequest("GET", url, nil)
			resp, err := app.Test(req)

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedStatus, resp.StatusCode)

			if tt.expectedStatus == http.StatusOK {
				var response GetApptsForLoadResponse
				err := json.NewDecoder(resp.Body).Decode(&response)
				assert.NoError(t, err)
				assert.NotNil(t, response.Appointments)
			}
		})
	}
}
