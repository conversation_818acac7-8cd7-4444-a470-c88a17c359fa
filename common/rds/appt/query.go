package appt

import (
	"context"

	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

func GetByServiceIDAccountAndFreightTrackingIDForEmail(
	ctx context.Context,
	serviceID uint,
	account,
	freightTrackingID string,
) (appt models.Appointment, err error) {

	return appt, rds.WithContext(ctx).
		Where("service_id = ?", serviceID).
		Where("account = ?", account).
		Where("freight_tracking_id = ?", freightTrackingID).
		Where("email_body IS NOT NULL").
		First(&appt).Error
}

// GetApptsByFreightTrackingIDAndServiceID gets all appointments for a load by freight tracking ID and service ID
// with integration preloaded to get tenant information
func GetApptsByFreightTrackingIDAndServiceID(
	ctx context.Context,
	freightTrackingID string,
	serviceID uint,
) (appts []models.Appointment, err error) {

	return appts, rds.WithContextReader(ctx).
		Where("freight_tracking_id = ?", freightTrackingID).
		Where("service_id = ?", serviceID).
		Preload("Integration").
		Find(&appts).Error
}
