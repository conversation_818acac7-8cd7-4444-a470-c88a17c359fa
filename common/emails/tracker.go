package emails

import (
	"regexp"
	"slices"
	"strings"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/tms/aljex"
	"github.com/drumkitai/drumkit/common/integrations/tms/ascend"
	"github.com/drumkitai/drumkit/common/integrations/tms/freightflow"
	"github.com/drumkitai/drumkit/common/integrations/tms/globaltranztms"
	"github.com/drumkitai/drumkit/common/integrations/tms/mcleodenterprise"
	"github.com/drumkitai/drumkit/common/integrations/tms/quantumedge"
	"github.com/drumkitai/drumkit/common/integrations/tms/relay"
	"github.com/drumkitai/drumkit/common/integrations/tms/revenova"
	"github.com/drumkitai/drumkit/common/integrations/tms/tai"
	"github.com/drumkitai/drumkit/common/integrations/tms/threeg"
	"github.com/drumkitai/drumkit/common/integrations/tms/turvo"
	"github.com/drumkitai/drumkit/common/models"
)

// Enables handling diff types of ids (idType should be the API filter that the TMS defines)
type tmsFreightID struct {
	IDType, ID string
}

func deduplicate[T comparable](sliceList []T) []T {
	allKeys := make(map[T]bool)
	list := []T{}
	for _, item := range sliceList {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			list = append(list, item)
		}
	}
	return list
}

// TODO: Deduplicate regex definitions here and in common/rds/integration/query.go
// Different tenants are at different places in sequence (NFI = 7{6}, Able = 6{5}, MLM={5}, TMKA = {6})
var aljexNFIPattern = regexp.MustCompile(`\b2\d{6}\b`)
var aljexAblePattern = regexp.MustCompile(`\b1\d{5}\b`)
var aljexMLMPattern = regexp.MustCompile(`\b(\d{5}|1\d{5})\b`)
var aljexTumaloPattern = regexp.MustCompile(`\b\d{6}\b`)

// NFI Relay load numbers are 7 digits starting with 3
var relayLoadNumberPattern = regexp.MustCompile(`\b(2|3)\d{6}\b`)

// NFI Relay carrier bookingIDs are 7 digits starting with 8
var relayBookingIDPattern = regexp.MustCompile(`\b(7|8)\d{6}\b`)

// GlobalTranzTMS orderIDs are 8 digits starting with 3
var globalTranzTMSEightDigitPattern = regexp.MustCompile(`\b3\d{7}\b`)

var turvoCustomIDPattern = regexp.MustCompile(`\b([a-zA-Z\d]{5,}-\d{5})\b`)
var turvoScNumberPattern = regexp.MustCompile(`\bSC\d+\b`)
var ascendCustomIDPattern = regexp.MustCompile(`\b\d{4}\b`)
var taiCustomIDPattern = regexp.MustCompile(`\b\d{9}\b`)

// Revenova load IDs can be 18-char Salesforce IDs or customer-specific formats like "Load-307453"
var revenovaIDPattern = regexp.MustCompile(`\b[a-zA-Z0-9]{18}\b|\bLoad-\d{6}\b|\b[A-Z]{2,}-\d{3,}\b|\bREF-\d{5}\b`)

var fiveToTenDigitPattern = regexp.MustCompile(`\b\d{5,10}\b`)
var freightFlowLoadIDPattern = regexp.MustCompile(`\b\d{5}-\d{2,3}\b`)

// 3G TMS load IDs are 6-8 digits starting with WA
var threeGPattern = regexp.MustCompile(`\b[A-Z]{2,4}\d{6,8}\b`)

// 3G TMS order IDs are in format ***********
var threeGOrderPattern = regexp.MustCompile(`\bCO-\d{8}\b`)

// 0(6|7|8)XXXXXX = Trident and 00(3|4)XXXX = Fetch Freight
var mcleodLoadIDPattern = regexp.MustCompile(`\b\d{7}\b|\b00(3|4|5)\d{4}\b`)
var mcleodMovementIDPattern = regexp.MustCompile(`\b\d{6}\b`)

var genericRefNumberPattern = regexp.MustCompile(`\b[a-zA-Z0-9\-_#]{5,20}\b`)

func getAljexIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) (res []TMSFreightIDForIntegration) {
	searchIn := email.Subject + "\n" + email.Body

	var pattern *regexp.Regexp
	switch strings.ToLower(tmsIntegration.Tenant) {
	case "nfi":
		pattern = aljexNFIPattern
	case "abts":
		pattern = aljexAblePattern
	case "mmhd":
		pattern = aljexMLMPattern
	case "tmka":
		pattern = aljexTumaloPattern
	default:
		return res
	}
	pros := helpers.Deduplicate(pattern.FindAllString(searchIn, -1))
	for _, pro := range pros {
		res = append(res, TMSFreightIDForIntegration{
			FreightID:   tmsFreightID{IDType: aljex.PROIDType, ID: pro},
			integration: tmsIntegration,
		})
	}

	// Customer refs are multi-digit numbers
	customerRefs := getNFICustomerRefs(email)
	// Aljex PROs are 7 digits so helpers.Deduplicate from list of candidate customer refs
	dedupedRefs := slices.DeleteFunc(customerRefs, func(ref string) bool {
		return slices.Contains(pros, ref)
	})

	for _, ref := range dedupedRefs {
		res = append(res, TMSFreightIDForIntegration{
			FreightID:   tmsFreightID{IDType: aljex.CustomerRefIDType, ID: ref},
			integration: tmsIntegration,
		})

	}

	return res
}

func getNFICustomerRefs(email *models.IngestedEmail) []string {
	searchIn := email.Subject + "\n" + email.Body

	return helpers.Deduplicate(fiveToTenDigitPattern.FindAllString(searchIn, -1))
}

func getTurvoFreightTrackingIDs(email *models.IngestedEmail) []string {
	searchIn := email.Subject + "\n" + email.Body

	return helpers.Deduplicate(turvoCustomIDPattern.FindAllString(searchIn, -1))
}

func getTurvoPoNumbers(email *models.IngestedEmail) []string {
	// turvo PO numbers can either be SC#s or 5-10 digit IDs - this function searches for both
	// and returns a list of all we found combined
	searchIn := email.Subject + "\n" + email.Body

	// remove all TurvoFreightTrackIDs
	removeIDs := getTurvoFreightTrackingIDs(email)
	pattern := strings.Join(removeIDs, "|")
	pattern = regexp.QuoteMeta(pattern)
	regex := regexp.MustCompile(pattern)
	searchIn = regex.ReplaceAllString(searchIn, "")

	scNumbers := helpers.Deduplicate(turvoScNumberPattern.FindAllString(searchIn, -1))
	fiveToTenDigitIDs := helpers.Deduplicate(fiveToTenDigitPattern.FindAllString(searchIn, -1))

	return append(scNumbers, fiveToTenDigitIDs...)
}

func getTurvoIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration

	// collect any freight tracking IDs we found, "customIDs" as Turvo defines them
	for _, id := range getTurvoFreightTrackingIDs(email) {
		customID := tmsFreightID{
			IDType: turvo.CustomIDType,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   customID,
			},
		)
	}

	// collect any PO numbers we found
	for _, id := range getTurvoPoNumbers(email) {
		poNumID := tmsFreightID{
			IDType: turvo.PONumIDType,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   poNumID,
			},
		)
	}
	return freightIDsForIntegration
}

func getAscendFreightTrackingIDs(email *models.IngestedEmail) []string {
	searchIn := email.Subject + "\n" + email.Body

	return helpers.Deduplicate(ascendCustomIDPattern.FindAllString(searchIn, -1))
}

func getAscendIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration

	// collect any Load IDs we found
	for _, id := range getAscendFreightTrackingIDs(email) {
		loadID := tmsFreightID{
			IDType: ascend.LoadIDType,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   loadID,
			},
		)
	}

	return freightIDsForIntegration
}

func getTaiFreightTrackingIDs(email *models.IngestedEmail) []string {
	searchIn := email.Subject + "\n" + email.Body

	return helpers.Deduplicate(taiCustomIDPattern.FindAllString(searchIn, -1))
}

func getRevenovaIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration

	searchIn := email.Subject + "\n" + email.Body

	for _, id := range deduplicate(revenovaIDPattern.FindAllString(searchIn, -1)) {
		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   tmsFreightID{IDType: revenova.LoadIDType, ID: id},
			},
		)
	}

	return freightIDsForIntegration
}

func getTaiIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration

	// collect any Load IDs we found
	for _, id := range getTaiFreightTrackingIDs(email) {
		loadID := tmsFreightID{
			IDType: tai.LoadIDType,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   loadID,
			},
		)
	}

	return freightIDsForIntegration
}

func getGlobalTranzTMSBOLNumbers(email *models.IngestedEmail) []string {
	searchIn := email.Subject + "\n" + email.Body

	return helpers.Deduplicate(globalTranzTMSEightDigitPattern.FindAllString(searchIn, -1))
}

func getGlobalTranzTMSFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration

	// Collect any BOL numbers found
	for _, id := range getGlobalTranzTMSBOLNumbers(email) {
		bolID := tmsFreightID{
			IDType: globaltranztms.BOLNumberType,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   bolID,
			},
		)
	}

	return freightIDsForIntegration
}

func getThreeGTMSFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration
	searchIn := email.Subject + "\n" + email.Body

	// Search for load IDs
	loadIDs := helpers.Deduplicate(threeGPattern.FindAllString(searchIn, -1))
	for _, id := range loadIDs {
		loadID := tmsFreightID{
			IDType: threeg.LoadNum,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   loadID,
			},
		)
	}

	// Search for order IDs
	orderIDs := helpers.Deduplicate(threeGOrderPattern.FindAllString(searchIn, -1))
	for _, id := range orderIDs {
		orderID := tmsFreightID{
			IDType: threeg.OrderNum,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   orderID,
			},
		)
	}

	return freightIDsForIntegration
}

func getRelayIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration
	searchIn := email.Subject + "\n" + email.Body

	loadNumbers := helpers.Deduplicate(relayLoadNumberPattern.FindAllString(searchIn, -1))
	// Collect any Relay reference/load numbers
	for _, id := range loadNumbers {
		loadID := tmsFreightID{
			IDType: relay.LoadNumber,
			ID:     id,
		}
		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   loadID,
			},
		)
	}

	// Collect any booking IDs we found
	bookingIDs := helpers.Deduplicate(relayBookingIDPattern.FindAllString(searchIn, -1))
	for _, id := range bookingIDs {
		bookingID := tmsFreightID{
			IDType: relay.BookingID,
			ID:     id,
		}
		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   bookingID,
			},
		)
	}

	return freightIDsForIntegration
}

func getMcleodIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {

	searchIn := email.Subject + "\n" + email.Body

	mcleodPatterns := map[string]*regexp.Regexp{
		mcleodenterprise.LoadIDType:      mcleodLoadIDPattern,
		mcleodenterprise.MovementIDType:  mcleodMovementIDPattern,
		mcleodenterprise.RefNumberIDType: genericRefNumberPattern,
	}

	var freightIDsForIntegration []TMSFreightIDForIntegration
	numRegex := regexp.MustCompile(`\d.+`)
	urlPattern := regexp.MustCompile(`https?://[^\s]+`)

	for idType, pattern := range mcleodPatterns {
		cleanedSearchIn := searchIn
		// RefNumberIDType is very permissive so filter out URLs (we leave potential TMS URLs to loads)
		if idType == mcleodenterprise.RefNumberIDType {
			cleanedSearchIn = urlPattern.ReplaceAllString(searchIn, "")
		}
		ids := helpers.Deduplicate(pattern.FindAllString(cleanedSearchIn, -1))

		for _, id := range ids {
			// RefNumberIDType is permissive and accepts any alpha and/or numeric strings because Go regex
			// doesn't support look-aheads. To avoid overloading our system with lookups of plain words like "hello",
			// we verify the candidate ID has at least one numer. Otherwise, we skip it.
			if idType == mcleodenterprise.RefNumberIDType && !numRegex.MatchString(id) {
				continue
			}

			customID := tmsFreightID{
				IDType: idType,
				ID:     id,
			}

			freightIDsForIntegration = append(
				freightIDsForIntegration,
				TMSFreightIDForIntegration{
					integration: tmsIntegration,
					FreightID:   customID,
				},
			)
		}
	}

	return freightIDsForIntegration
}

func getFreightFlowIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration
	searchIn := email.Subject + "\n" + email.Body

	// Search for load IDs (they are UUIDs)
	loadIDs := helpers.Deduplicate(freightFlowLoadIDPattern.FindAllString(searchIn, -1))
	for _, id := range loadIDs {
		loadID := tmsFreightID{
			IDType: freightflow.LoadIDType,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   loadID,
			},
		)
	}

	return freightIDsForIntegration
}

func getQuantumEdgeIDsFromEmail(
	email *models.IngestedEmail,
	tmsIntegration models.Integration,
) []TMSFreightIDForIntegration {
	var freightIDsForIntegration []TMSFreightIDForIntegration
	searchIn := email.Subject + "\n" + email.Body

	// Search for shipment IDs (7 digits starting with 13)
	shipmentIDs := helpers.Deduplicate(fiveToTenDigitPattern.FindAllString(searchIn, -1))
	for _, id := range shipmentIDs {
		loadID := tmsFreightID{
			IDType: quantumedge.FreightTrackingIDType,
			ID:     id,
		}

		freightIDsForIntegration = append(
			freightIDsForIntegration,
			TMSFreightIDForIntegration{
				integration: tmsIntegration,
				FreightID:   loadID,
			},
		)
	}

	// Extract reference numbers from pickup/delivery sections
	refNumbers := genericGetRefNumbers(email, tmsIntegration, quantumedge.RefNumberIDType)
	freightIDsForIntegration = append(freightIDsForIntegration, refNumbers...)

	return freightIDsForIntegration
}

func genericGetRefNumbers(
	email *models.IngestedEmail,
	tmsObj models.Integration,
	tmsIDType string,
) (tmsIDCandidates []TMSFreightIDForIntegration) {

	numRegex := regexp.MustCompile(`\d.+`)
	urlPattern := regexp.MustCompile(`https?://[^\s]+`)
	searchIn := email.Subject + "\n" + email.Body

	// RefNumberIDType is very permissive so filter out URLs (we leave potential TMS URLs to loads)
	cleanedSearchIn := urlPattern.ReplaceAllString(searchIn, "")
	ids := helpers.Deduplicate(genericRefNumberPattern.FindAllString(cleanedSearchIn, -1))

	for _, id := range ids {
		// RefNumberIDType is permissive and accepts any alpha and/or numeric strings because Go regex
		// doesn't support look-aheads. To avoid overloading our system with lookups of plain words like "hello",
		// we verify the candidate ID has at least one numer. Otherwise, we skip it.
		if !numRegex.MatchString(id) {
			continue
		}

		customID := tmsFreightID{
			IDType: tmsIDType,
			ID:     id,
		}

		tmsIDCandidates = append(
			tmsIDCandidates,
			TMSFreightIDForIntegration{
				integration: tmsObj,
				FreightID:   customID,
			},
		)
	}

	return tmsIDCandidates
}
