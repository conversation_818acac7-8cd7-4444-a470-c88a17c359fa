package extractor

import "github.com/drumkitai/drumkit/common/models"

const (
	PromptPickupTimeInstructions                  = "pickup_time_instructions"
	PromptPickupInstructions                      = "pickup_instructions"
	PromptPickupExamples                          = "pickup_examples"
	PromptConsigneeTimeInstructions               = "consignee_time_instructions"
	PromptConsigneeInstructions                   = "consignee_instructions"
	PromptConsigneeExamples                       = "consignee_examples"
	PromptSpecificationsTransportTypeInstructions = "specifications_transport_type_instructions"
	PromptBusinessHoursInstructions               = "business_hours_instructions"
)

// promptTemplates maps TMS name -> prompt type -> prompt content
var promptTemplates = map[string]map[string]string{
	string(models.Aljex): {
		PromptBusinessHoursInstructions: aljexBusinessHoursInstructions,
		PromptPickupTimeInstructions:    aljexPickupTimeInstructions,
		PromptPickupExamples:            aljexPickupExamples,
		PromptConsigneeTimeInstructions: aljexConsigneeTimeInstructions,
		PromptConsigneeExamples:         aljexConsigneeExamples,
	},
	string(models.McleodEnterprise): {
		PromptPickupTimeInstructions:    mcleodPickupTimeInstructions,
		PromptPickupInstructions:        mcleodPickupInstructions,
		PromptPickupExamples:            mcleodPickupExamples,
		PromptConsigneeTimeInstructions: mcleodConsigneeTimeInstructions,
		PromptConsigneeInstructions:     mcleodConsigneeInstructions,
		PromptConsigneeExamples:         mcleodConsigneeExamples,
	},
	string(models.Turvo): {
		PromptPickupTimeInstructions:                  turvoPickupTimeInstructions,
		PromptPickupInstructions:                      turvoPickupInstructions,
		PromptPickupExamples:                          turvoPickupExamples,
		PromptConsigneeTimeInstructions:               turvoConsigneeTimeInstructions,
		PromptConsigneeInstructions:                   turvoConsigneeInstructions,
		PromptConsigneeExamples:                       turvoConsigneeExamples,
		PromptSpecificationsTransportTypeInstructions: turvoSpecificationsTransportTypeInstructions,
	},
	"default": {
		PromptPickupTimeInstructions:                  defaultPickupTimeInstructions,
		PromptPickupInstructions:                      defaultPickupInstructions,
		PromptPickupExamples:                          defaultPickupExamples,
		PromptConsigneeTimeInstructions:               defaultConsigneeTimeInstructions,
		PromptConsigneeInstructions:                   defaultConsigneeInstructions,
		PromptConsigneeExamples:                       defaultConsigneeExamples,
		PromptSpecificationsTransportTypeInstructions: defaultSpecificationsTransportTypeInstructions,
		PromptBusinessHoursInstructions:               defaultBusinessHoursInstructions,
	},
}

func getPrompt(tmsName models.IntegrationName, promptType string) string {
	if tmsPrompts, ok := promptTemplates[string(tmsName)]; ok {
		if prompt, ok := tmsPrompts[promptType]; ok {
			return prompt
		}
	}
	if defaultPrompts, ok := promptTemplates["default"]; ok {
		if prompt, ok := defaultPrompts[promptType]; ok {
			return prompt
		}
	}
	return ""
}
