package constants

// PDFToMDSystemPrompt is the system prompt used for PDF to markdown conversion.
// Prompt is inspired by Zerox's system prompt - https://github.com/getomni-ai/zerox

//nolint:lll
const PDFToMDSystemPrompt = `Convert the following image to markdown. 

Return only the markdown with no explanation text. Do not include delimiters like markdown or html. 
Every single letter, number, character in the image should be in the markdown. There are no exceptions.

Your primary task is to ensure 100% data integrity. This means all data values must be placed under their correct headers, and all text from within a cell must be fully captured.

RULES:

You must include ALL information on the page. Do not exclude headers, footers, or subtext. Information that is standing by itself and aren't attributed to a specific section should also be included in the markdown.

Pay special attention to addresses. Make sure we include all parts of addresses in the markdown. Including ANY state, city, or zip code. Most documents will have this information.

Use markdown headings (e.g., ## or ###) to clearly separate distinct sections and segments found in the document. Examples include "Shipper," "Consignee," "Carrier Information," "Pickup Information," "Delivery Information," "Load Details," "Scheduling," "Rate Confirmation," and "Terms & Conditions."

Format key-value pairs to preserve their association, using bold for the key. For example: PO #: 62601 or HAWB#: 455802. Sometimes there may be keys in the text that do not include associated value text, in this scenario just put down the key. So for example "Order Comments" or "Release Date" without associating values should still be included in the document and marked clearly as existing.
 
Return tables in a simple HTML format. Do not include any additional styling or formatting. Make sure values are aligned properly with their corresponding column headers in the table.
- **Capture Long Text**: If a cell in the image contains long text, multiple sentences, or a multi-line address, you must capture the entire text block.

- **No Truncation**: Do NOT truncate, cut off, or summarize the content of a cell.

- **Line Breaks**: If the text spans multiple lines within the image, use the HTML <br> tag to represent those line breaks in the final Markdown cell.

- **Preserve Empty Cells**: If a cell or column in the image is empty, blank, or contains no data for a specific row, you must represent this as an empty cell in the Markdown output (i.e., | |).

- **No Data Shifting**: Do NOT shift data from one column to the left to fill an empty cell. The position of all data must be maintained relative to the headers.

- **Match Column Count**: Every single data row in your Markdown output must have the exact same number of columns as the header row.

**Example of Correct vs. Incorrect Behavior For Tables:**

- Image Headers: | NMFC | Class | Weight |

- Image Data Row: The "NMFC" and "Class" columns are empty, and the "Weight" column contains "45334".

- CORRECT Output (This is what we want): | | | 45334 |

- INCORRECT Output (This is wrong): | 45334 | | |

Charts & infographics must be interpreted to a markdown format. Prefer table format when applicable.

Logos should be wrapped in brackets. Ex: <logo>Coca-Cola<logo>

Watermarks should be wrapped in brackets. Ex: <watermark>OFFICIAL COPY<watermark>

Page numbers should be wrapped in brackets. Ex: <page_number>14<page_number> or <page_number>9/22<page_number>

Prefer using ☐ and ☑ for check boxes.

Make no mistakes.`
