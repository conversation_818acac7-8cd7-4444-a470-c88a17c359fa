package frontclient

type FrontEventType string

const (
	FrontEventTypeComment  FrontEventType = "comment"
	FrontEventTypeInbound  FrontEventType = "inbound"
	FrontEventTypeOutbound FrontEventType = "outbound"
	FrontEventTypeOutReply FrontEventType = "out_reply"
)

var AcceptedFrontEventTypes = []FrontEventType{
	FrontEventTypeComment,
	FrontEventTypeInbound,
}

type (
	EmailRelatedUsers struct {
		From string
		To   string
		CC   []string
		BCC  []string
	}

	// MetaLinks represents the common _links structure used across different objects
	MetaLinks struct {
		Self    string            `json:"self,omitempty"`
		Related map[string]string `json:"related,omitempty"`
	}

	// CustomFields represents a generic map of custom field key-value pairs
	CustomFields map[string]any

	// Meta represents the metadata describing the type of an object
	Meta struct {
		Type string `json:"type"`
	}

	// Source represents the source of an event (could be a rule or inbox)
	SourceData struct {
		MetaLinks    MetaLinks    `json:"_links"`
		ID           string       `json:"id"`
		Name         string       `json:"name,omitempty"`
		Actions      [][]string   `json:"actions,omitempty"`
		IsPrivate    bool         `json:"is_private"`
		Email        string       `json:"email,omitempty"`
		Username     string       `json:"username,omitempty"`
		FirstName    string       `json:"first_name,omitempty"`
		LastName     string       `json:"last_name,omitempty"`
		IsAdmin      bool         `json:"is_admin,omitempty"`
		IsAvailable  bool         `json:"is_available,omitempty"`
		IsBlocked    bool         `json:"is_blocked,omitempty"`
		Type         string       `json:"type,omitempty"`
		CustomFields CustomFields `json:"custom_fields,omitempty"`
	}

	Source struct {
		Meta Meta `json:"_meta"`
		Data any  `json:"data"`
	}

	Target struct {
		Meta Meta    `json:"_meta"`
		Data Message `json:"data"`
	}

	// Tag represents a conversation tag
	Tag struct {
		MetaLinks                    MetaLinks `json:"_links"`
		ID                           string    `json:"id"`
		Name                         string    `json:"name"`
		Description                  string    `json:"description"`
		Highlight                    *string   `json:"highlight"`
		IsPrivate                    bool      `json:"is_private"`
		IsVisibleInConversationLists bool      `json:"is_visible_in_conversation_lists"`
		CreatedAt                    float64   `json:"created_at"`
		UpdatedAt                    float64   `json:"updated_at"`
	}

	// Link represents external links associated with a conversation
	Link struct {
		MetaLinks    MetaLinks    `json:"_links"`
		ID           string       `json:"id"`
		Name         string       `json:"name"`
		Type         string       `json:"type"`
		ExternalURL  string       `json:"external_url"`
		CustomFields CustomFields `json:"custom_fields,omitempty"`
	}

	// ScheduledReminder represents a scheduled reminder for a conversation
	ScheduledReminder struct {
		MetaLinks   MetaLinks `json:"_links"`
		CreatedAt   float64   `json:"created_at"`
		ScheduledAt float64   `json:"scheduled_at"`
		UpdatedAt   float64   `json:"updated_at"`
	}

	// Recipient represents the conversation recipient
	Recipient struct {
		MetaLinks MetaLinks `json:"_links"`
		Name      string    `json:"name"`
		Handle    string    `json:"handle"`
		Role      string    `json:"role"`
	}

	// Conversation represents a Front API conversation object
	Conversation struct {
		MetaLinks          MetaLinks           `json:"_links"`
		ID                 string              `json:"id"`
		Subject            string              `json:"subject"`
		Status             string              `json:"status"`
		StatusID           string              `json:"status_id"`
		StatusCategory     string              `json:"status_category"`
		TicketIDs          []string            `json:"ticket_ids"`
		Assignee           Source              `json:"assignee"`
		Recipient          Recipient           `json:"recipient"`
		Tags               []Tag               `json:"tags"`
		Links              []Link              `json:"links"`
		CustomFields       CustomFields        `json:"custom_fields"`
		CreatedAt          float64             `json:"created_at"`
		WaitingSince       float64             `json:"waiting_since"`
		IsPrivate          bool                `json:"is_private"`
		ScheduledReminders []ScheduledReminder `json:"scheduled_reminders"`
		Metadata           struct {
			ExternalConversationIDs []string `json:"external_conversation_ids"`
		} `json:"metadata"`
	}

	// Event represents the top-level Front API event response
	FrontEvent struct {
		MetaLinks    MetaLinks      `json:"_links"`
		ID           string         `json:"id"`
		Type         FrontEventType `json:"type"`
		Source       Source         `json:"source"`
		Target       Target         `json:"target"`
		Conversation Conversation   `json:"conversation"`
	}

	Message struct {
		MetaLinks   MetaLinks      `json:"_links"`
		ID          string         `json:"id"`
		Type        FrontEventType `json:"type"`
		IsInbound   bool           `json:"is_inbound"`
		CreatedAt   float64        `json:"created_at"`
		Blurb       string         `json:"blurb"`
		Body        string         `json:"body"`
		Text        string         `json:"text"`
		ErrorType   string         `json:"error_type"`
		Version     string         `json:"version"`
		Subject     string         `json:"subject"`
		DraftMode   string         `json:"draft_mode"`
		Metadata    any            `json:"metadata"`
		Author      any            `json:"author"`
		Recipients  []Recipient    `json:"recipients"`
		Attachments []any          `json:"attachments"`
		Signature   any            `json:"signature"`
		IsDraft     bool           `json:"is_draft"`
	}

	Pagination struct {
		Next string `json:"next"`
	}

	ChannelListResponse struct {
		Pagination Pagination `json:"_pagination"`
		Links      MetaLinks  `json:"_links"`
		Results    []Channel  `json:"_results"`
	}

	// A Channel in Front represents an entry point that redirects to configured shared inboxes
	Channel struct {
		MetaLinks MetaLinks       `json:"_links"`
		ID        string          `json:"id"`
		Name      string          `json:"name"`
		Address   string          `json:"address"`
		SendAs    string          `json:"send_as"`
		Type      string          `json:"type"`
		Settings  ChannelSettings `json:"settings"`
		IsPrivate bool            `json:"is_private"`
		IsValid   bool            `json:"is_valid"`
	}

	ChannelSettings struct {
		AllTeammatesCanReply bool `json:"all_teammates_can_reply"`
	}

	// SendMessageRequest represents the request body for sending a message via Front API
	SendMessageRequest struct {
		To      []string `json:"to"`
		Cc      []string `json:"cc,omitempty"`
		Bcc     []string `json:"bcc,omitempty"`
		Subject string   `json:"subject"`
		Body    string   `json:"body"`
		Sender  string   `json:"sender,omitempty"`
	}

	// SendMessageResponse represents the response from Front API when sending a message
	SendMessageResponse struct {
		MetaLinks  MetaLinks   `json:"_links"`
		ID         string      `json:"id"`
		Type       string      `json:"type"`
		CreatedAt  float64     `json:"created_at"`
		Subject    string      `json:"subject"`
		Body       string      `json:"body"`
		Text       string      `json:"text"`
		Recipients []Recipient `json:"recipients"`
	}
)
