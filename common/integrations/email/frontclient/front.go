package frontclient

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"slices"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

// Incognito host is used for requests when service doesn't include tenant subdomain
const IncognitoHost = "https://api2.frontapp.com"

// FrontChannelMessagesURL is the URL template for sending messages to a Front channel
const FrontChannelMessagesURL = "https://%s.api.frontapp.com/channels/%s/messages"

type (
	// Client is the set of exposed Front operations used by Drumkit services
	Client interface {
		GetMessageByID(ctx context.Context, messageURL string, service models.Service) (*Message, error)
		GetChannelList(ctx context.Context, service models.Service) ([]Channel, error)
		GetServiceEmailAddressesAssignedToChannels(
			ctx context.Context,
			service models.Service,
			channels []Channel,
		) (emailAddresses []string)
		SendMessage(
			ctx context.Context,
			genEmail *models.GeneratedEmail,
			service models.Service,
		) (*SendMessageResponse, error)
	}

	Service struct {
		client *http.Client

		// HACK: Only used for response logging; email creds are stored in user table
		integration models.Integration
	}
)

func New() (Client, error) {
	return &Service{
		client: otel.TracingHTTPClient(),
		integration: models.Integration{
			Type: models.EmailType,
			Name: models.Front,
		},
	}, nil
}

// Get message info from Front
func (s *Service) GetMessageByID(ctx context.Context, messageURL string, service models.Service) (*Message, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, messageURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error building GetMessageByID Front request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+service.FrontAuthToken)

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return nil, fmt.Errorf("error sending POST token request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading body: %w", err)
	}

	var msg Message
	if err = json.Unmarshal(body, &msg); err != nil {
		return nil, fmt.Errorf("error unmarshaling token resp: %w", err)
	}

	return &msg, nil
}

func redisFrontChannelsKey(serviceID uint) string {
	return fmt.Sprintf("front-channels-service-id-%d", serviceID)
}

func (s *Service) GetChannelList(ctx context.Context, service models.Service) ([]Channel, error) {
	var redisKey = redisFrontChannelsKey(service.ID)

	cachedChannelList, _, err := redis.GetKey[[]Channel](ctx, redisKey)
	if err == nil && cachedChannelList != nil {
		log.Info(ctx, "found cached list of Front channels in Redis", zap.String("service name", service.Name))
		return cachedChannelList, nil
	}

	log.Info(ctx, "couldn't find cached list of Front channels, proceeding to fetch")

	channels, err := s.fetchAllChannels(ctx, service)
	if err != nil {
		return nil, err
	}

	if err = redis.SetKey(ctx, redisKey, channels, 30*time.Minute); err != nil {
		log.WarnNoSentry(ctx, "error setting cached list of Front channels in redis", zap.Error(err))
		return nil, err
	}

	log.Info(ctx, "cached list of Front channels in redis successfully", zap.Any("redisKey", redisKey))

	return channels, nil
}

func (s *Service) fetchAllChannels(ctx context.Context, service models.Service) ([]Channel, error) {
	var channels []Channel
	var channelListResp ChannelListResponse
	nextURL := fmt.Sprintf("https://%s.api.frontapp.com/channels", service.FrontTenantSubdomain)

	for {
		err := s.get(ctx, nextURL, service.FrontAuthToken, &channelListResp)
		if err != nil {
			return nil, fmt.Errorf("error getting channel list: %w", err)
		}

		channels = append(channels, channelListResp.Results...)
		nextURL = channelListResp.Pagination.Next

		if nextURL == "" {
			break
		}
	}

	return channels, nil
}

func (s *Service) GetServiceEmailAddressesAssignedToChannels(
	ctx context.Context,
	service models.Service,
	channels []Channel,
) (emailAddresses []string) {
	for _, channel := range channels {
		// Messages from Front API return recipient email addresses in lowercase, however channel email addresses
		// may be returned in CamelCase. We convert them to lowercase to ensure we match the correct email address.
		lowercaseChannelAddress := strings.ToLower(channel.Address)
		channelAddressParts := strings.Split(lowercaseChannelAddress, "@")

		if len(channelAddressParts) != 2 {
			log.WarnNoSentry(
				ctx,
				"channel address is not a valid email address",
				zap.String("channel address", lowercaseChannelAddress),
			)
			continue
		}

		emailDomain := fmt.Sprintf("@%s", channelAddressParts[1])

		if slices.Contains(service.EmailDomains, strings.ToLower(emailDomain)) {
			emailAddresses = append(emailAddresses, lowercaseChannelAddress)
		}
	}

	return emailAddresses
}

func (s *Service) get(ctx context.Context, url string, auth string, out any) error {
	return s.do(ctx, http.MethodGet, url, auth, nil, out)
}

func (s *Service) do(ctx context.Context, method, url string, auth string, body any, out any) (err error) {
	var bodyBytes []byte
	if body != nil {
		if bodyBytes, err = json.Marshal(body); err != nil {
			return fmt.Errorf("error marshaling body: %w", err)
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to build %s %s request: %w", method, url, err)
	}

	if auth != "" {
		req.Header.Add("Authorization", "Bearer "+auth)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	resp, err := s.client.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, s.integration, err)
		return fmt.Errorf("failed to send %s %s request: %w", method, url, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, s.integration, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read %s response body: %w", url, err)
	}

	if code := resp.StatusCode; code >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(s.integration, req, resp, respBody)
	}

	if out != nil {
		if err := json.Unmarshal(respBody, out); err != nil {
			log.Error(
				ctx,
				"json unmarshal failed for Front response body",
				zap.ByteString("body", respBody),
			)

			return fmt.Errorf("%s %s json unmarshal failed: %w", method, url, err)
		}
	}

	return nil
}

// GetChannelByEmailAddress finds a channel that matches the given email address.
// First tries to find an exact match, then falls back to finding a channel
// whose domain matches the sender's email domain and is in the service's email domains.
func (s *Service) GetChannelByEmailAddress(
	ctx context.Context,
	service models.Service,
	emailAddress string,
) (*Channel, error) {
	channels, err := s.GetChannelList(ctx, service)
	if err != nil {
		return nil, fmt.Errorf("error getting channel list: %w", err)
	}

	emailParts := strings.Split(emailAddress, "@")
	if len(emailParts) != 2 {
		return nil, fmt.Errorf("invalid email address format: %s", emailAddress)
	}
	senderDomain := fmt.Sprintf("@%s", emailParts[1])

	for i := range channels {
		if strings.EqualFold(channels[i].Address, emailAddress) {
			return &channels[i], nil
		}
	}

	for i := range channels {
		channelParts := strings.Split(channels[i].Address, "@")
		if len(channelParts) != 2 {
			continue
		}
		channelDomain := fmt.Sprintf("@%s", channelParts[1])

		if strings.EqualFold(channelDomain, senderDomain) &&
			slices.ContainsFunc(service.EmailDomains, func(domain string) bool {
				return strings.EqualFold(domain, channelDomain)
			}) {
			return &channels[i], nil
		}
	}

	for i := range channels {
		channelParts := strings.Split(channels[i].Address, "@")
		if len(channelParts) != 2 {
			continue
		}
		channelDomain := fmt.Sprintf("@%s", channelParts[1])

		if slices.ContainsFunc(service.EmailDomains, func(domain string) bool {
			return strings.EqualFold(domain, channelDomain)
		}) {
			log.Info(
				ctx,
				"using fallback channel for sender email",
				zap.String("senderEmail", emailAddress),
				zap.String("channelAddress", channels[i].Address),
			)
			return &channels[i], nil
		}
	}

	return nil, fmt.Errorf(
		"no channel found for email address %s: no channels match service email domains",
		emailAddress,
	)
}

// SendMessage sends a message via Front API
func (s *Service) SendMessage(
	ctx context.Context,
	genEmail *models.GeneratedEmail,
	service models.Service,
) (*SendMessageResponse, error) {

	channel, err := s.GetChannelByEmailAddress(ctx, service, genEmail.Sender)
	if err != nil {
		return nil, fmt.Errorf("error finding channel for sender %s: %w", genEmail.Sender, err)
	}

	reqBody := SendMessageRequest{
		To:      genEmail.Recipients,
		Subject: genEmail.Subject,
		Body:    genEmail.Body,
	}

	if len(genEmail.CC) > 0 {
		reqBody.Cc = genEmail.CC
	}

	url := fmt.Sprintf(FrontChannelMessagesURL, service.FrontTenantSubdomain, channel.ID)

	var resp SendMessageResponse
	err = s.do(ctx, http.MethodPost, url, service.FrontAuthToken, reqBody, &resp)
	if err != nil {
		return nil, fmt.Errorf("error sending message via Front API: %w", err)
	}

	genEmail.ExternalID = resp.ID
	// TODO: Front API may return conversation ID in _links.related.conversation
	// TODO: Support attachments - Front API supports attachments via multipart/form-data
	genEmail.ThreadID = ""
	genEmail.Status = models.SentStatus
	genEmail.SentAt = models.ToValidNullTime(time.Now())

	return &resp, nil
}
