package main

import (
	"time"

	"github.com/drumkitai/drumkit/common/models"
)

var sampleUpdatedLoad = models.Load{
	FreightTrackingID: "2080005",
	ServiceID:         1,
	LoadCoreInfo: models.LoadCoreInfo{
		Status: "hold",
		RateData: models.RateData{
			FSCPercent: models.Ptr(float32(5)),
		},
		Customer: models.Customer{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         "7-11 C/O NFI LOGISTICS",
				AddressLine1: "1515 BURNT MILL ROAD",
				City:         "CHERRY HILL",
				State:        "NJ",
				Zipcode:      "08003",
				Phone:        "(*************",
			},
		},
		BillTo: models.BillTo{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         "7-11 C/O NFI LOGISTICS",
				AddressLine1: "1515 BURNT MILL ROAD",
				City:         "CHERRY HILL",
				State:        "NJ",
				Zipcode:      "08003",
			},
		},
		Pickup: models.Pickup{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name: "TEST 123",
				// New values
				AddressLine1: "455 NEW ADDRESS",
				City:         "BOSTON",
				State:        "MA",
				Zipcode:      "08003",

				Contact: "JANE DOE",
				Phone:   "(*************",
				Email:   "<EMAIL>",
			},
			// New value
			ApptStartTime: models.NullTime{
				Time:  time.Date(2023, 10, 20, 10, 30, 0, 0, time.UTC),
				Valid: true,
			},
		},
		Consignee: models.Consignee{
			CompanyCoreInfo: models.CompanyCoreInfo{
				Name:         "TEST 123",
				AddressLine1: "1515 BURNT MILL ROAD",
				City:         "CHERRY HILL",
				State:        "NJ",
				Zipcode:      "08003",
				Contact:      "JOHN SMITH",
				Phone:        "(*************",
				Email:        "<EMAIL>",
			},
			ApptStartTime: models.NullTime{
				Time:  time.Now(),
				Valid: true,
			},
			ApptNote: "Opendock 499999",
		},
		Carrier: models.Carrier{
			Phone:                "(*************",
			FirstDriverPhone:     "(*************",
			DispatchCity:         "BOSTON",
			ExternalTMSTruckID:   "123",
			ExternalTMSTrailerID: "AAAA-1234567",
		},
	},
}
