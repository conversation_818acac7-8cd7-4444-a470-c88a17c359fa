package turvo

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

func (t *Turvo) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (checkcalls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(t.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryTurvo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	load, err := loadDB.GetLoadByID(ctx, loadID)
	if err != nil {
		return checkcalls, fmt.Errorf("get loadDB failed: %w", err)
	}

	shipmentID, err := strconv.Atoi(load.ExternalTMSID)
	if err != nil {
		return checkcalls, fmt.Errorf("error converting ExternalTMSID to int: %w", err)
	}

	var appResponse AppTurvoShipmentResponse
	fullURL := "https://app.turvo.com/api/shipments/" + strconv.Itoa(shipmentID)

	appQueryParams := url.Values{}
	appQueryParams.Set("types", `["general","status"]`)
	appQueryParams.Set("event", "join")

	err = t.getWithAuth(ctx, fullURL, appQueryParams, &appResponse, s3backup.TypeCheckCalls)
	if err == nil && appResponse.Details.ShipmentID > 0 {
		checkcalls = t.parseAppTurvoStatusHistory(ctx, appResponse, loadID, freightTrackingID, load)
		if len(checkcalls) > 0 {
			return checkcalls, nil
		}
		log.Debug(ctx, "no status history found in app.turvo.com response, falling back to current status")
	}
	queryParams := url.Values{}
	queryParams.Add("fullResponse", "true")

	var response GetCheckCallResp
	endPoint := fmt.Sprintf("v1/shipments/%s", load.ExternalTMSID)

	err = t.getWithAuth(ctx, endPoint, queryParams, &response, s3backup.TypeCheckCalls)
	if err != nil {
		return checkcalls, err
	}
	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return checkcalls, fmt.Errorf("%s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	checkcalls = []models.CheckCall{t.turvoShipmentToCheckCall(response, loadID, freightTrackingID)}

	return checkcalls, nil
}

func (t *Turvo) PostCheckCall(
	ctx context.Context,
	load *models.Load,
	checkcall models.CheckCall,
) (err error) {

	spanAttrs := append(otel.IntegrationAttrs(t.tms), otel.SafeIntAttribute("load_id", load.ID))
	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallTurvo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody, err := t.toTurvoCheckCall(checkcall, load)
	if err != nil {
		return fmt.Errorf("error creating turvo check call body: %w", err)
	}

	queryParams := url.Values{}
	queryParams.Add("fullResponse", "true")
	endPoint := fmt.Sprintf("v1/shipments/status/%s", load.ExternalTMSID)

	var response LoadResponse
	err = t.putWithAuth(ctx, endPoint, queryParams, reqBody, &response, s3backup.TypeCheckCalls)
	if err != nil {
		return fmt.Errorf("posting check call failed: %w", err)
	}

	if strings.EqualFold(response.Status, "error") { // TODO: use errtypes.HTTPResponseError
		return fmt.Errorf("posting check call failed: %s - %s",
			response.Details.ErrorCode, response.Details.ErrorMessage)
	}

	return nil
}

func (t *Turvo) toTurvoCheckCall(cc models.CheckCall, load *models.Load) (result UpdateCheckCallsRequest, err error) {
	shipmentID, err := strconv.Atoi(load.ExternalTMSID)
	if err != nil {
		return result, fmt.Errorf("error converting shipmentID to int: %w", err)
	}
	result.ID = shipmentID

	code := getCodeFromStatus(cc.Status)
	if code <= 0 {
		return result, fmt.Errorf("failed to get code for status %s", cc.Status)
	}

	result.Status.Location.City = strings.Join([]string{cc.City, cc.State}, ", ")

	result.Status.Code.Key = fmt.Sprint(code)
	result.Status.Code.Value = cc.Status
	// NOTE: If API is called at 2 pm but the check call was at 1 pm, then Turvo displays 1 pm as the "reported date",
	//  but the timeline tab is in order of *when* the API request was made, which in this case is 2 pm.
	result.Status.StatusDate.Date = cc.DateTime.Time
	result.Status.StatusDate.Timezone = cc.Timezone

	result.Status.Notes = cc.Notes
	result.Status.Timezone = cc.Timezone

	message := strings.ToLower(cc.Status)
	switch message {
	case "at pickup", "picked up":
		pickupID, err := strconv.Atoi(load.Pickup.ExternalTMSID)
		if err != nil {
			return result, fmt.Errorf("error converting pickup ID to int: %w", err)
		}

		result.Status.GlobalShipLocationID = pickupID

	case "at delivery", "delivered":
		deliveryID, err := strconv.Atoi(load.Consignee.ExternalTMSID)
		if err != nil {
			return result, fmt.Errorf("error converting consignee ID to int: %w", err)
		}

		result.Status.GlobalShipLocationID = deliveryID

	}

	return result, nil
}

func (t *Turvo) turvoShipmentToCheckCall(
	response GetCheckCallResp,
	loadID uint,
	freightTrackingID string,
) models.CheckCall {

	status := response.Details.Status

	cityState := strings.Split(status.Location.City, ",")

	var city, state string
	if len(cityState) > 0 {
		city = strings.TrimSpace(cityState[0])
	}
	if len(cityState) > 1 {
		state = strings.TrimSpace(cityState[1])
	}

	return models.CheckCall{
		LoadID:            loadID,
		FreightTrackingID: freightTrackingID,
		Status:            status.Code.Value,
		DateTime: models.NullTime{
			Time:  status.StatusDate.Date,
			Valid: true,
		},
		DateTimeWithoutTimezone: models.NullTime{
			Time:  status.StatusDate.Date,
			Valid: true,
		},
		Lat:      status.Location.Lat,
		Lon:      status.Location.Lon,
		City:     city,
		State:    state,
		Notes:    status.Notes,
		Timezone: status.StatusDate.Timezone,
	}
}

// parseAppTurvoStatusHistory extracts check call history from app.turvo.com API response
func (t *Turvo) parseAppTurvoStatusHistory(
	ctx context.Context,
	appResponse AppTurvoShipmentResponse,
	loadID uint,
	freightTrackingID string,
	load models.Load,
) []models.CheckCall {
	var checkcalls []models.CheckCall

	details := appResponse.Details
	status := details.Status

	if status.LastUpdatedOn.After(time.Time{}) {
		var city, state string
		if status.Attributes.City != nil {
			city = status.Attributes.City.Name
		}
		if status.Attributes.Address != nil {
			state = status.Attributes.Address.State.Name
		}

		var lat, lon float64
		if status.Attributes.GPS != nil && len(status.Attributes.GPS.Coordinates) >= 2 {
			lat = float64(status.Attributes.GPS.Coordinates[1])
			lon = float64(status.Attributes.GPS.Coordinates[0])
		}

		checkCall := models.CheckCall{
			LoadID:            loadID,
			FreightTrackingID: freightTrackingID,
			Status:            status.Description,
			DateTime: models.NullTime{
				Time:  status.LastUpdatedOn,
				Valid: true,
			},
			DateTimeWithoutTimezone: models.NullTime{
				Time:  status.LastUpdatedOn,
				Valid: true,
			},
			Lat:      lat,
			Lon:      lon,
			City:     city,
			State:    state,
			Notes:    status.Notes,
			Timezone: "UTC",
		}
		checkcalls = append(checkcalls, checkCall)
	}

	if status.DispatchedDate != nil && status.DispatchedDate.Date.After(time.Time{}) {
		var city, state string
		if status.Attributes.City != nil {
			city = status.Attributes.City.Name
		}
		if status.Attributes.Address != nil {
			state = status.Attributes.Address.State.Name
		}

		timezone := "UTC"
		if status.DispatchedDate.Timezone != "" {
			timezone = status.DispatchedDate.Timezone
		}

		checkCall := models.CheckCall{
			LoadID:            loadID,
			FreightTrackingID: freightTrackingID,
			Status:            "Dispatched",
			DateTime: models.NullTime{
				Time:  status.DispatchedDate.Date,
				Valid: true,
			},
			DateTimeWithoutTimezone: models.NullTime{
				Time:  status.DispatchedDate.Date,
				Valid: true,
			},
			City:     city,
			State:    state,
			Notes:    status.Notes,
			Timezone: timezone,
		}
		checkcalls = append(checkcalls, checkCall)
	}

	if details.GlobalRoute.ShipLocations != nil {
		shipLocations := details.GlobalRoute.ShipLocations
		numLocations := len(shipLocations)
		var firstLocID, lastLocID int
		var deliveryLocID int
		if numLocations > 0 {
			firstLocID = shipLocations[0].GlobalShipLocationID
			if numLocations > 1 {
				lastLocID = shipLocations[numLocations-1].GlobalShipLocationID
			}
		}

		// Get delivery location ID for single-location shipments
		if numLocations == 1 && load.Consignee.ExternalTMSID != "" {
			if id, err := strconv.Atoi(load.Consignee.ExternalTMSID); err == nil {
				deliveryLocID = id
			}
		}

		for _, shipLoc := range shipLocations {
			if shipLoc.Attributes.Arrival != nil {
				arrival := shipLoc.Attributes.Arrival
				if arrival.Date != nil && arrival.Date.After(time.Time{}) {
					city := shipLoc.Address.City.Name
					state := shipLoc.Address.State.Name

					var lat, lon float64
					if shipLoc.Address.GPS != nil && len(shipLoc.Address.GPS.Coordinates) >= 2 {
						lat = float64(shipLoc.Address.GPS.Coordinates[1])
						lon = float64(shipLoc.Address.GPS.Coordinates[0])
					}

					statusText := determineArrivalStatus(
						numLocations,
						shipLoc.GlobalShipLocationID,
						deliveryLocID,
						firstLocID,
						lastLocID,
					)

					timezone := "UTC"
					if arrival.Timezone != nil {
						timezone = *arrival.Timezone
					}

					checkCall := models.CheckCall{
						LoadID:            loadID,
						FreightTrackingID: freightTrackingID,
						Status:            statusText,
						DateTime: models.NullTime{
							Time:  *arrival.Date,
							Valid: true,
						},
						DateTimeWithoutTimezone: models.NullTime{
							Time:  *arrival.Date,
							Valid: true,
						},
						Lat:      lat,
						Lon:      lon,
						City:     city,
						State:    state,
						Timezone: timezone,
					}
					checkcalls = append(checkcalls, checkCall)
				}
			}

			if shipLoc.Attributes.Departed != nil {
				departed := shipLoc.Attributes.Departed
				if departed.Date != nil && departed.Date.After(time.Time{}) {
					city := shipLoc.Address.City.Name
					state := shipLoc.Address.State.Name

					var lat, lon float64
					if shipLoc.Address.GPS != nil && len(shipLoc.Address.GPS.Coordinates) >= 2 {
						lat = float64(shipLoc.Address.GPS.Coordinates[1])
						lon = float64(shipLoc.Address.GPS.Coordinates[0])
					}

					timezone := "UTC"
					if departed.Timezone != nil {
						timezone = *departed.Timezone
					}

					checkCall := models.CheckCall{
						LoadID:            loadID,
						FreightTrackingID: freightTrackingID,
						Status:            "Departed",
						DateTime: models.NullTime{
							Time:  *departed.Date,
							Valid: true,
						},
						DateTimeWithoutTimezone: models.NullTime{
							Time:  *departed.Date,
							Valid: true,
						},
						Lat:      lat,
						Lon:      lon,
						City:     city,
						State:    state,
						Timezone: timezone,
					}
					checkcalls = append(checkcalls, checkCall)
				}
			}
		}
	}

	log.Debug(ctx, "parsed status history from app.turvo.com",
		zap.Int("check_calls_found", len(checkcalls)),
		zap.String("freight_tracking_id", freightTrackingID),
	)

	return checkcalls
}

// determineArrivalStatus determines the status text for an arrival check call
// based on the number of locations and the location's position in the route.
func determineArrivalStatus(
	numLocations int,
	shipLocID int,
	deliveryLocID int,
	firstLocID int,
	lastLocID int,
) string {
	switch {
	case numLocations == 1 && shipLocID == deliveryLocID:
		// Single location that matches delivery location
		return "At delivery"
	case numLocations > 0 && shipLocID == firstLocID:
		// First location (or single location that doesn't match delivery)
		return "At pickup"
	case numLocations > 1 && shipLocID == lastLocID:
		// Last location when there are multiple locations
		return "At delivery"
	default:
		return "Arrived"
	}
}

func getCodeFromStatus(status string) int {
	switch strings.ToLower(status) {
	case "quote active":
		return 2100
	case "tendered":
		return 2101
	case "covered":
		return 2102
	case "dispatched":
		return 2103
	case "at pickup":
		return 2104
	case "en route":
		return 2105
	case "at delivery":
		return 2106
	case "route complete":
		return 2116
	case "delivered":
		return 2107
	case "ready for billing":
		return 2108
	case "processing":
		return 2109
	case "carrier paid":
		return 2110
	case "customer paid":
		return 2111
	case "completed":
		return 2112
	case "canceled":
		return 2113
	case "tender - offered":
		return 2117
	case "tender - accepted":
		return 2118
	case "tender - rejected":
		return 2119
	case "quote inactive":
		return 2114
	case "picked up":
		return 2115
	default:
		return -1
	}
}

var StatusKeyEnums = []int{
	2100,
	2101,
	2102,
	2103,
	2104,
	2105,
	2106,
	2116,
	2107,
	2108,
	2109,
	2110,
	2111,
	2112,
	2113,
	2117,
	2118,
	2119,
	2114,
	2115,
}

var StatusMessageEnums = []string{
	"Quote active",
	"Tendered",
	"Covered",
	"Dispatched",
	"At pickup",
	"En route",
	"At delivery",
	"Route Complete",
	"Delivered",
	"Ready for billing",
	"Processing",
	"Carrier paid",
	"Customer paid",
	"Completed",
	"Canceled",
	"Tender - offered",
	"Tender - accepted",
	"Tender - rejected",
	"Quote inactive",
	"Picked up",
}
