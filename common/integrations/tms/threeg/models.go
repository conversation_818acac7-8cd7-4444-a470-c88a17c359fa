package threeg

import "encoding/xml"

// =============================================================================
// BASIC/UTILITY TYPES
// =============================================================================

type ValueWithUOM struct {
	Value string `xml:",chardata"`
	UOM   string `xml:"uom,attr"`
}

type CurrencyValueWithBase struct {
	CurrencyValue ValueWithUOM `xml:"CurrencyValue"`
	CurrencyBase  ValueWithUOM `xml:"CurrencyBase"`
}

// =============================================================================
// ENTITY METADATA
// =============================================================================

type EntityHeader struct {
	DateCreated      string `xml:"DateCreated"`
	CreatedBy        string `xml:"CreatedBy"`
	DateLastModified string `xml:"DateLastModified"`
	LastModifiedBy   string `xml:"LastModifiedBy"`
}

type Qualifier struct {
	QualifierName string `xml:"QualifierName"`
	Description   string `xml:"Description"`
	QualifierType string `xml:"QualifierType"`
}

// =============================================================================
// CONTACT & ADDRESS
// =============================================================================

type Contact struct {
	ContactNum  string `xml:"ContactNum"`
	ContactName string `xml:"ContactName"`
	Phone1      string `xml:"Phone1"`
	Email       string `xml:"Email"`
	IsActive    bool   `xml:"IsActive"`
}

type Address struct {
	Line1   string `xml:"line1"`
	Line2   string `xml:"line2"`
	City    string `xml:"city"`
	State   string `xml:"state"`
	Zipcode string `xml:"zipcode"`
	Country string `xml:"country"`
}

// =============================================================================
// TRADING PARTNERS
// =============================================================================

type TradingPartner struct {
	TradingPartnerName string `xml:"TradingPartnerName"`
	TradingPartnerNum  string `xml:"TradingPartnerNum"`
}

type TradingPartnerCarrier struct {
	EntityHeader         EntityHeader         `xml:"EntityHeader"`
	OrganizationName     string               `xml:"OrganizationName"`
	TradingPartnerName   string               `xml:"TradingPartnerName"`
	TradingPartnerNum    string               `xml:"TradingPartnerNum"`
	TradingPartnerType   string               `xml:"TradingPartnerType"`
	IsActive             bool                 `xml:"IsActive"`
	Currency             string               `xml:"Currency"`
	TradingPartnerDetail TradingPartnerDetail `xml:"TradingPartnerDetail"`
}

type TradingPartnerDetail struct {
	TradingPartnerCarrier TradingPartnerCarrierDetail `xml:"TradingPartnerCarrier"`
}

type TradingPartnerCarrierDetail struct {
	Scac                        string        `xml:"Scac"`
	MccNum                      string        `xml:"MccNum"`
	MccNumPrefix                string        `xml:"MccNumPrefix"`
	InterstateCcID              string        `xml:"InterstateCcId"`
	ApVendorNum                 string        `xml:"ApVendorNum"`
	HasW9                       bool          `xml:"HasW9"`
	ProNumAutoAssignAction      string        `xml:"ProNumAutoAssignAction"`
	TrackingLinkURL             string        `xml:"TrackingLinkUrl"`
	IsRailway                   bool          `xml:"IsRailway"`
	IsRailDrayage               bool          `xml:"IsRailDrayage"`
	IsPortDrayage               bool          `xml:"IsPortDrayage"`
	ServicesAirport             bool          `xml:"ServicesAirport"`
	HasTiaWatchdogReport        bool          `xml:"HasTiaWatchdogReport"`
	IsUiaaCertified             bool          `xml:"IsUiaaCertified"`
	IsCarbCompliant             bool          `xml:"IsCarbCompliant"`
	IsSmartWayCertified         bool          `xml:"IsSmartWayCertified"`
	IsCtpatCertified            bool          `xml:"IsCtpatCertified"`
	HasTwicDrivers              bool          `xml:"HasTwicDrivers"`
	HasTsaDrivers               bool          `xml:"HasTsaDrivers"`
	IsFastCertified             bool          `xml:"IsFastCertified"`
	HasTeams                    bool          `xml:"HasTeams"`
	HasReeferEquipment          bool          `xml:"HasReeferEquipment"`
	HandlesOverweight           bool          `xml:"HandlesOverweight"`
	HandlesOversize             bool          `xml:"HandlesOversize"`
	HasTankerEndorsement        bool          `xml:"HasTankerEndorsement"`
	IsHazmatCertified           bool          `xml:"IsHazmatCertified"`
	IsBonded                    bool          `xml:"IsBonded"`
	HazmatCertificationNum      string        `xml:"HazmatCertificationNum"`
	TransportationExportBondNum string        `xml:"TransportationExportBondNum"`
	WsibNum                     string        `xml:"WsibNum"`
	WsibExpirationDate          string        `xml:"WsibExpirationDate"`
	LockIsActive                bool          `xml:"LockIsActive"`
	LockDoNotAssign             bool          `xml:"LockDoNotAssign"`
	IsFreightForwarder          bool          `xml:"IsFreightForwarder"`
	CarrierSafety               CarrierSafety `xml:"CarrierSafety"`
}

// =============================================================================
// CARRIER & SAFETY
// =============================================================================

type CarrierSafety struct {
	PublicLiabilityLimit                  CurrencyValueWithBase `xml:"PublicLiabilityLimit"`
	PublicLiabilityExpiration             string                `xml:"PublicLiabilityExpiration"`
	ProductLiabilityLimit                 CurrencyValueWithBase `xml:"ProductLiabilityLimit"`
	ProductLiabilityExpiration            string                `xml:"ProductLiabilityExpiration"`
	WorkersCompLimit                      CurrencyValueWithBase `xml:"WorkersCompLimit"`
	WorkersCompExpiration                 string                `xml:"WorkersCompExpiration"`
	WaiveWorkersComp                      bool                  `xml:"WaiveWorkersComp"`
	AutoInsuranceLimit                    CurrencyValueWithBase `xml:"AutoInsuranceLimit"`
	AutoInsuranceExpiration               string                `xml:"AutoInsuranceExpiration"`
	DataLastUpdated                       string                `xml:"DataLastUpdated"`
	DataCurrentAsOf                       string                `xml:"DataCurrentAsOf"`
	UnsafeDriving                         string                `xml:"UnsafeDriving"`
	UnderInvestigationUnsafeDriving       bool                  `xml:"UnderInvestigationUnsafeDriving"`
	ScoreUnsafeDriving                    string                `xml:"ScoreUnsafeDriving"`
	IsAlertOnUnsafeDriving                bool                  `xml:"IsAlertOnUnsafeDriving"`
	FatiguedDriving                       string                `xml:"FatiguedDriving"`
	UnderInvestigationFatiguedDriving     bool                  `xml:"UnderInvestigationFatiguedDriving"`
	ScoreFatiguedDriving                  string                `xml:"ScoreFatiguedDriving"`
	IsAlertOnFatiguedDriving              bool                  `xml:"IsAlertOnFatiguedDriving"`
	DriverFitness                         string                `xml:"DriverFitness"`
	UnderInvestigationDriverFitness       bool                  `xml:"UnderInvestigationDriverFitness"`
	ScoreDriverFitness                    string                `xml:"ScoreDriverFitness"`
	IsAlertOnDriverFitness                bool                  `xml:"IsAlertOnDriverFitness"`
	ControlledSubstance                   string                `xml:"ControlledSubstance"`
	UnderInvestigationControlledSubstance bool                  `xml:"UnderInvestigationControlledSubstance"`
	ScoreControlledSubstance              string                `xml:"ScoreControlledSubstance"`
	IsAlertOnControlledSubstance          bool                  `xml:"IsAlertOnControlledSubstance"`
	VehicleMaintenance                    string                `xml:"VehicleMaintenance"`
	UnderInvestigationVehicleMaintenance  bool                  `xml:"UnderInvestigationVehicleMaintenance"`
	ScoreVehicleMaintenance               string                `xml:"ScoreVehicleMaintenance"`
	IsAlertOnVehicleMaintenance           bool                  `xml:"IsAlertOnVehicleMaintenance"`
	BypassCarrierSafetyValidation         bool                  `xml:"BypassCarrierSafetyValidation"`
	BypassCarrierInsuranceValidation      bool                  `xml:"BypassCarrierInsuranceValidation"`
	IsMonitored                           bool                  `xml:"IsMonitored"`
	PowerUnitCount                        string                `xml:"PowerUnitCount"`
	DriverCount                           string                `xml:"DriverCount"`
	DistAnnual                            string                `xml:"Dist_Annual"`
	AnnualDistanceYear                    string                `xml:"AnnualDistanceYear"`
	IsCargoCoverageRequired               bool                  `xml:"IsCargoCoverageRequired"`
	IsBondSuretyRequired                  bool                  `xml:"IsBondSuretyRequired"`
	HasBrokerBond                         bool                  `xml:"HasBrokerBond"`
	SafeyReviewDueDate                    string                `xml:"SafeyReviewDueDate"`
	OutOfServiceDate                      string                `xml:"OutOfServiceDate"`
	RevocationDate                        string                `xml:"RevocationDate"`
	ReinstateDate                         string                `xml:"ReinstateDate"`
	DotProfileDate                        string                `xml:"DotProfileDate"`
	IntraStateOperatingNum                string                `xml:"IntraStateOperatingNum"`
	RemediationPlanStatus                 string                `xml:"RemediationPlanStatus"`
	RemediationReviewDate                 string                `xml:"RemediationReviewDate"`
	SeaDriverScore                        string                `xml:"SeaDriverScore"`
	SeaVehicleScore                       string                `xml:"SeaVehicleScore"`
	SeaManagementScore                    string                `xml:"SeaManagementScore"`
	FmcsaSafetyRatingDate                 string                `xml:"FmcsaSafetyRatingDate"`
	FmcsaSafetyReviewDate                 string                `xml:"FmcsaSafetyReviewDate"`
	IsContractCarrierPending              bool                  `xml:"IsContractCarrierPending"`
	IsContractCarrierRevoked              bool                  `xml:"IsContractCarrierRevoked"`
	IsCommonCarrierPending                bool                  `xml:"IsCommonCarrierPending"`
	IsCommonCarrierRevoked                bool                  `xml:"IsCommonCarrierRevoked"`
	IsBrokerPending                       bool                  `xml:"IsBrokerPending"`
	IsBrokerRevoked                       bool                  `xml:"IsBrokerRevoked"`
	DoNotAssign                           bool                  `xml:"DoNotAssign"`
	HasSeriousViolation                   bool                  `xml:"HasSeriousViolation"`
	BipdRequiredAmount                    CurrencyValueWithBase `xml:"BipdRequiredAmount"`
	CarrierAssignmentStatusSafety         string                `xml:"CarrierAssignmentStatusSafety"`
	CarrierAssignmentStatusInsurance      string                `xml:"CarrierAssignmentStatusInsurance"`
	SafetyServiceOverrideDate             string                `xml:"SafetyServiceOverrideDate"`
	SafetyServiceCsaBasics                string                `xml:"SafetyServiceCsaBasics"`
	SafetyServiceInspectionDate           string                `xml:"SafetyServiceInspectionDate"`
	SafetyServiceInsCertificatesDate      string                `xml:"SafetyServiceInsCertificatesDate"`
	SafetyServiceDotInsuranceDate         string                `xml:"SafetyServiceDotInsuranceDate"`
	HasAuthorityMexico                    bool                  `xml:"HasAuthorityMexico"`
	HasAuthorityCanada                    bool                  `xml:"HasAuthorityCanada"`
	HasReeferBreakdownInsurance           bool                  `xml:"HasReeferBreakdownInsurance"`
}

// =============================================================================
// LOCATION & STOPS
// =============================================================================

type Company struct {
	ID      string  `xml:"id"`
	Name    string  `xml:"name"`
	Address Address `xml:"address"`
	Contact string  `xml:"contact"`
	Phone   string  `xml:"phone"`
	Email   string  `xml:"email"`
}

type Location struct {
	Company
	RefNumber    string `xml:"refNumber"`
	ApptRequired bool   `xml:"apptRequired"`
	ApptType     string `xml:"apptType"`
	ApptStart    string `xml:"apptStart"`
	ApptEnd      string `xml:"apptEnd"`
	ApptNote     string `xml:"apptNote"`
	Timezone     string `xml:"timezone"`
}

type DeliveryLocation struct {
	Location
	MustDeliver string `xml:"mustDeliver"`
}

type StopLocation struct {
	OrganizationName string `xml:"OrganizationName"`
	LocNum           string `xml:"LocNum"`
	AddrName         string `xml:"AddrName"`
	Addr1            string `xml:"Addr1"`
	Addr2            string `xml:"Addr2"`
	CityName         string `xml:"CityName"`
	StateCode        string `xml:"StateCode"`
	CountryISO2      string `xml:"CountryISO2"`
	PostalCode       string `xml:"PostalCode"`
	Latitude         string `xml:"Latitude"`
	Longitude        string `xml:"Longitude"`
}

type Stop struct {
	StopNum           int          `xml:"StopNum"`
	StopType          string       `xml:"StopType"`
	PlannedArrival    string       `xml:"PlannedArrival"`
	PlannedDeparture  string       `xml:"PlannedDeparture"`
	ExpectedArrival   string       `xml:"ExpectedArrival"`
	ExpectedDeparture string       `xml:"ExpectedDeparture"`
	ActualArrival     string       `xml:"ActualArrival"`
	ActualDeparture   string       `xml:"ActualDeparture"`
	Appointment       string       `xml:"Appointment"`
	StopLocInfo       StopLocation `xml:"StopLocInfo"`
	OrderNums         OrderNums    `xml:"OrderNums"`
}

type StopList struct {
	Stop []Stop `xml:"Stop"`
}

// =============================================================================
// SPECIFICATIONS & RATES
// =============================================================================

type Rate struct {
	Type        string  `xml:"type"`
	LineHaul    float32 `xml:"lineHaul"`
	NumUnits    float32 `xml:"numUnits"`
	Unit        string  `xml:"unit"`
	TotalCharge float32 `xml:"totalCharge"`
	TotalCost   float32 `xml:"totalCost"`
	Currency    string  `xml:"currency"`
}

type Specifications struct {
	TotalPallets   int     `xml:"totalPallets"`
	TotalPieces    int     `xml:"totalPieces"`
	Commodities    string  `xml:"commodities"`
	TotalWeight    float32 `xml:"totalWeight"`
	WeightUnit     string  `xml:"weightUnit"`
	TotalDistance  float32 `xml:"totalDistance"`
	DistanceUnit   string  `xml:"distanceUnit"`
	TransportType  string  `xml:"transportType"`
	TransportSize  string  `xml:"transportSize"`
	IsRefrigerated bool    `xml:"isRefrigerated"`
	MinTemp        float32 `xml:"minTemp"`
	MaxTemp        float32 `xml:"maxTemp"`
	Hazmat         bool    `xml:"hazmat"`
}

// =============================================================================
// ORDERS
// =============================================================================

type (
	TotalDistanceValue struct {
		DistanceValue ValueWithUOM `xml:"DistanceValue"`
	}

	TotalGrossWeight struct {
		WeightValue ValueWithUOM `xml:"WeightValue"`
	}

	TotalNetWeight struct {
		WeightValue ValueWithUOM `xml:"WeightValue"`
	}

	TotalGrossVolume struct {
		VolumeValue ValueWithUOM `xml:"VolumeValue"`
	}

	TotalNetVolume struct {
		VolumeValue ValueWithUOM `xml:"VolumeValue"`
	}

	TotalNetFreightCharge struct {
		CurrencyValue ValueWithUOM `xml:"CurrencyValue"`
	}

	TotalNetAccessorialCharge struct {
		CurrencyValue ValueWithUOM `xml:"CurrencyValue"`
	}

	TotalNetCharge struct {
		CurrencyValue ValueWithUOM `xml:"CurrencyValue"`
	}

	TotalNetFreightCost struct {
		CurrencyValue ValueWithUOM `xml:"CurrencyValue"`
	}

	TotalNetAccessorialCost struct {
		CurrencyValue ValueWithUOM `xml:"CurrencyValue"`
	}

	TotalNetCost struct {
		CurrencyValue ValueWithUOM `xml:"CurrencyValue"`
	}
)

type OrderNums struct {
	OrderNum []string `xml:"OrderNum"`
}

type OrderList struct {
	Order []Order `xml:"Order"`
}

type Order struct {
	EntityHeader              EntityHeader              `xml:"EntityHeader"`
	OrganizationName          string                    `xml:"OrganizationName"`
	OrdNum                    string                    `xml:"OrdNum"`
	OrdType                   string                    `xml:"OrdType"`
	FreightTerms              string                    `xml:"FreightTerms"`
	OrderTMSStatus            string                    `xml:"OrderTMSStatus"`
	IsPrePayment              bool                      `xml:"IsPrePayment"`
	ScheduledEarlyPickup      string                    `xml:"ScheduledEarlyPickup"`
	ScheduledLatePickup       string                    `xml:"ScheduledLatePickup"`
	TotalGrossWeight          TotalGrossWeight          `xml:"TotalGrossWeight"`
	TotalNetWeight            TotalNetWeight            `xml:"TotalNetWeight"`
	TotalGrossVolume          TotalGrossVolume          `xml:"TotalGrossVolume"`
	TotalNetVolume            TotalNetVolume            `xml:"TotalNetVolume"`
	TotalPieceCount           int                       `xml:"TotalPieceCount"`
	Currency                  string                    `xml:"Currency"`
	TotalNetFreightCharge     TotalNetFreightCharge     `xml:"TotalNetFreightCharge"`
	TotalNetAccessorialCharge TotalNetAccessorialCharge `xml:"TotalNetAccessorialCharge"`
	TotalNetCharge            TotalNetCharge            `xml:"TotalNetCharge"`
	IsHazmat                  bool                      `xml:"IsHazmat"`
	BillingStatus             string                    `xml:"BillingStatus"`
	Client                    TradingPartner            `xml:"Client"`
	OrderComments             struct {
		Comment []struct {
			Qualifier    Qualifier `xml:"Qualifier"`
			CommentValue string    `xml:"CommentValue"`
		} `xml:"Comment"`
	} `xml:"OrderComments"`
	OrderRefNums struct {
		RefNum []struct {
			Qualifier   Qualifier `xml:"Qualifier"`
			RefNumValue string    `xml:"RefNumValue"`
		} `xml:"RefNum"`
	} `xml:"OrderRefNums"`
	DestinationContact Contact `xml:"DestinationContact"`
}

// =============================================================================
// LOADS
// =============================================================================

type LoadList struct {
	Load []Load `xml:"Load"`
}

// =============================================================================
// LOAD HANDLING UNITS
// =============================================================================

type LoadHandlingUnits struct {
	LoadHandlingUnit []LoadHandlingUnit `xml:"LoadHandlingUnit"`
}

type LoadHandlingUnit struct {
	HandlingUnit    HandlingUnit `xml:"HandlingUnit"`
	StopNumPickup   int          `xml:"StopNumPickup"`
	StopNumDelivery int          `xml:"StopNumDelivery"`
}

type HandlingUnit struct {
	HandlingUnitNum               string            `xml:"HandlingUnitNum"`
	TotalWeightGrossPlanned       WeightWithBase    `xml:"TotalWeightGrossPlanned"`
	TotalWeightNetPlanned         WeightWithBase    `xml:"TotalWeightNetPlanned"`
	TotalVolumeGrossPlanned       VolumeWithBase    `xml:"TotalVolumeGrossPlanned"`
	TotalVolumeNetPlanned         VolumeWithBase    `xml:"TotalVolumeNetPlanned"`
	TotalPieceCountPlanned        int               `xml:"TotalPieceCountPlanned"`
	TotalHandlingUnitCountPlanned int               `xml:"TotalHandlingUnitCountPlanned"`
	Flex1TotalPlanned             float64           `xml:"Flex1TotalPlanned"`
	Flex2TotalPlanned             float64           `xml:"Flex2TotalPlanned"`
	HandlingUnitLines             HandlingUnitLines `xml:"HandlingUnitLines"`
}

type HandlingUnitLines struct {
	HandlingUnitLine []HandlingUnitLine `xml:"HandlingUnitLine"`
}

type HandlingUnitLine struct {
	OrdNum                    string            `xml:"OrdNum"`
	OrdLineNum                string            `xml:"OrdLineNum"`
	WeightGrossPlanned        WeightWithBase    `xml:"WeightGrossPlanned"`
	WeightNetPlanned          WeightWithBase    `xml:"WeightNetPlanned"`
	VolumeGrossPlanned        VolumeWithBase    `xml:"VolumeGrossPlanned"`
	VolumeNetPlanned          VolumeWithBase    `xml:"VolumeNetPlanned"`
	PieceCountPlanned         int               `xml:"PieceCountPlanned"`
	PieceType                 string            `xml:"PieceType"`
	FreightClassPlanned       string            `xml:"FreightClassPlanned"`
	DescriptionPlanned        string            `xml:"DescriptionPlanned"`
	HandlingUnitCountPlanned  int               `xml:"HandlingUnitCountPlanned"`
	HandlingUnitLengthPlanned DimensionWithBase `xml:"HandlingUnitLengthPlanned"`
	HandlingUnitWidthPlanned  DimensionWithBase `xml:"HandlingUnitWidthPlanned"`
	HandlingUnitHeightPlanned DimensionWithBase `xml:"HandlingUnitHeightPlanned"`
	IsHazmatPlanned           bool              `xml:"IsHazmatPlanned"`
	Flex1Planned              float64           `xml:"Flex1Planned"`
	Flex2Planned              float64           `xml:"Flex2Planned"`
	HandlingUnitTypeName      string            `xml:"HandlingUnitTypeName"`
	IsNonStackable            bool              `xml:"IsNonStackable"`
}

type WeightWithBase struct {
	WeightValue ValueWithUOM `xml:"WeightValue"`
	WeightBase  ValueWithUOM `xml:"WeightBase"`
}

type VolumeWithBase struct {
	VolumeValue ValueWithUOM `xml:"VolumeValue"`
	VolumeBase  ValueWithUOM `xml:"VolumeBase"`
}

type DimensionWithBase struct {
	DimensionValue ValueWithUOM `xml:"DimensionValue"`
	DimensionBase  ValueWithUOM `xml:"DimensionBase"`
}

// =============================================================================
// LOAD LINES
// =============================================================================

type LoadLines struct {
	LoadLine []LoadLine `xml:"LoadLine"`
}

type LoadLine struct {
	FreightClass              string         `xml:"FreightClass"`
	HandlingUnitTypeNamePiece string         `xml:"HandlingUnitTypeNamePiece"`
	Description               string         `xml:"Description"`
	IsHazmat                  bool           `xml:"IsHazmat"`
	IsNonStackable            bool           `xml:"IsNonStackable"`
	HandlingUnitCount         int            `xml:"HandlingUnitCount"`
	TotalWeightGross          WeightWithBase `xml:"TotalWeightGross"`
	TotalWeightNet            WeightWithBase `xml:"TotalWeightNet"`
	TotalVolumeGross          VolumeWithBase `xml:"TotalVolumeGross"`
	TotalVolumeNet            VolumeWithBase `xml:"TotalVolumeNet"`
	PieceCount                int            `xml:"PieceCount"`
	TotalFlex1                float64        `xml:"TotalFlex1"`
	TotalFlex2                float64        `xml:"TotalFlex2"`
}

type Load struct {
	EntityHeader             EntityHeader          `xml:"EntityHeader"`
	OrganizationName         string                `xml:"OrganizationName"`
	LoadNum                  string                `xml:"LoadNum"`
	FreightTerms             string                `xml:"FreightTerms"`
	PlannedStart             string                `xml:"PlannedStart"`
	PlannedEnd               string                `xml:"PlannedEnd"`
	EarliestPickup           string                `xml:"EarliestPickup"`
	TransitTime              int                   `xml:"TransitTime"`
	Currency                 string                `xml:"Currency"`
	TradingPartnerClientName string                `xml:"TradingPartnerClientName"`
	TradingPartnerCarrier    TradingPartnerCarrier `xml:"TradingPartnerCarrier"`
	Client                   struct {
		EntityHeader       EntityHeader `xml:"EntityHeader"`
		OrganizationName   string       `xml:"OrganizationName"`
		TradingPartnerName string       `xml:"TradingPartnerName"`
		TradingPartnerNum  string       `xml:"TradingPartnerNum"`
		TradingPartnerType string       `xml:"TradingPartnerType"`
		IsActive           bool         `xml:"IsActive"`
		Currency           string       `xml:"Currency"`
	} `xml:"Client"`

	TotalNetFreightCost         TotalNetFreightCost     `xml:"TotalNetFreightCost"`
	TotalNetAccessorialCost     TotalNetAccessorialCost `xml:"TotalNetAccessorialCost"`
	TotalNetCost                TotalNetCost            `xml:"TotalNetCost"`
	TotalDistance               TotalDistanceValue      `xml:"TotalDistance"`
	TotalNetWeight              TotalNetWeight          `xml:"TotalNetWeight"`
	TotalGrossWeight            TotalGrossWeight        `xml:"TotalGrossWeight"`
	TotalNetVolume              TotalNetVolume          `xml:"TotalNetVolume"`
	TotalGrossVolume            TotalGrossVolume        `xml:"TotalGrossVolume"`
	TotalPieceCount             int                     `xml:"TotalPieceCount"`
	TotalHandlingUnitCount      int                     `xml:"TotalHandlingUnitCount"`
	LoadTMSStatus               string                  `xml:"LoadTMSStatus"`
	TransportMode               string                  `xml:"TransportMode"`
	Stops                       StopList                `xml:"Stops"`
	AvailableEquipmentRatedName string                  `xml:"AvailableEquipmentRatedName"`
	LoadHandlingUnits           LoadHandlingUnits       `xml:"LoadHandlingUnits"`
	LoadLines                   LoadLines               `xml:"LoadLines"`
}

// =============================================================================
// RESPONSE TYPES
// =============================================================================

// ErrorResponse represents the error response from 3G TMS
type ErrorResponse struct {
	XMLName      xml.Name `xml:"ExportLoadsResponse"`
	Result       string   `xml:"Result"`
	ErrorMessage string   `xml:"ErrorMessage"`
	ExportError  struct {
		Message    string `xml:"Message"`
		Cause      string `xml:"Cause"`
		EntityType string `xml:"EntityType"`
		ThreadName string `xml:"ThreadName"`
		ServerName string `xml:"ServerName"`
		ServerTime string `xml:"ServerTime"`
	} `xml:"ExportError"`
}

type LoadResponse struct {
	XMLName    xml.Name         `xml:"LoadResponse"`
	LoadID     string           `xml:"loadId"`
	LoadNumber string           `xml:"loadNumber"`
	ProNumber  string           `xml:"proNumber"`
	BOLNumber  string           `xml:"bolNumber"`
	Status     string           `xml:"status"`
	Mode       string           `xml:"mode"`
	PONumbers  string           `xml:"poNumbers"`
	OrderNums  []string         `xml:"OrderNums>OrderNum"`
	Operator   string           `xml:"operator"`
	Customer   Company          `xml:"customer"`
	Pickup     Location         `xml:"pickup"`
	Delivery   DeliveryLocation `xml:"delivery"`
	Carrier    struct {
		ID        string `xml:"id"`
		Name      string `xml:"name"`
		MCNumber  string `xml:"mcNumber"`
		DOTNumber string `xml:"dotNumber"`
		SCAC      string `xml:"scac"`
		Phone     string `xml:"phone"`
		Email     string `xml:"email"`
		Notes     string `xml:"notes"`

		DriverName    string `xml:"driverName"`
		DriverPhone   string `xml:"driverPhone"`
		Dispatcher    string `xml:"dispatcher"`
		TruckNumber   string `xml:"truckNumber"`
		TrailerNumber string `xml:"trailerNumber"`
	} `xml:"carrier"`

	Specifications Specifications `xml:"specifications"`

	RateData struct {
		CustomerRate  Rate    `xml:"customerRate"`
		CarrierRate   Rate    `xml:"carrierRate"`
		FSCPercent    float32 `xml:"fscPercent"`
		NetProfit     float32 `xml:"netProfit"`
		ProfitPercent float32 `xml:"profitPercent"`
	} `xml:"rateData"`

	Notes  []Note    `xml:"notes>note"`
	Orders OrderList `xml:"Orders"`
}

type OrderResponse struct {
	XMLName        xml.Name         `xml:"OrderResponse"`
	OrderID        string           `xml:"orderId"`
	OrderNumber    string           `xml:"orderNumber"`
	Status         string           `xml:"status"`
	Mode           string           `xml:"mode"`
	PONumbers      string           `xml:"poNumbers"`
	Customer       Company          `xml:"customer"`
	Pickup         Location         `xml:"pickup"`
	Consignee      DeliveryLocation `xml:"consignee"`
	Specifications Specifications   `xml:"specifications"`
	RateData       struct {
		CustomerRate Rate `xml:"customerRate"`
	} `xml:"rateData"`
	Notes []Note `xml:"notes>note"`
}

type LoadData struct {
	XMLName   xml.Name `xml:"http://schemas.3gtms.com/tms/v1/tns LoadData"`
	BatchInfo struct {
		BatchDateTime   string `xml:"BatchDateTime"`
		SentBy          string `xml:"SentBy"`
		PageNum         int    `xml:"PageNum"`
		PageCnt         int    `xml:"PageCnt"`
		EcosystemAction string `xml:"EcosystemAction"`
	} `xml:"BatchInfo"`
	Loads  LoadList  `xml:"Loads"`
	Orders OrderList `xml:"Orders"`
}

// =============================================================================
// WEB API STRUCTS
// =============================================================================

// WebTradingPartner represents a trading partner from 3G TMS Web API
type WebTradingPartner struct {
	ID                int    `json:"id"`
	Name              string `json:"tradingPartnerName"`
	TradingPartnerNum string `json:"tradingPartnerNum"`
	Type              string `json:"tradingPartnerType"`
	AddressLine1      string `json:"corporateLocAddr1"`
	AddressLine2      string `json:"corporateLocAddr2"`
	City              string `json:"corporateLocCityName"`
	State             string `json:"corporateLocStateCode"`
	ZipCode           string `json:"corporateLocPostalCode"`
	Phone             string `json:"corporateLocPhone"`
	Email             string `json:"corporateLocEmail"`
}

// TradingPartnerListResponse represents the response from 3G TMS Web API
type WebTradingPartnerListResponse struct {
	Data []WebTradingPartner `json:"data"`
}

// CustomerFilter represents the filter for Customer type trading partners
type WebTradingPartnerRequestFilter struct {
	FieldName      string   `json:"fieldName"`
	FieldType      string   `json:"fieldType"`
	TranslatedName string   `json:"translatedName"`
	FieldNames     *string  `json:"fieldNames"`
	FieldIDs       []string `json:"fieldIds"`
	DataValueGroup string   `json:"dataValueGroup,omitempty"`
	Inclusive      *bool    `json:"inclusive,omitempty"`
	UserDateFormat string   `json:"userDateFormat"`
	Values         []string `json:"values"`
}

// =============================================================================
// QUOTES
// =============================================================================

type WeightNet struct {
	WeightValue ValueWithUOM `xml:"WeightValue"`
	WeightBase  ValueWithUOM `xml:"WeightBase"`
}

type QuoteLine struct {
	QuoteLineNum         string    `xml:"QuoteLineNum"`
	FreightClass         string    `xml:"FreightClass"`
	WeightNet            WeightNet `xml:"WeightNet"`
	IsFreightClassLocked string    `xml:"IsFreightClassLocked"`
}

type QuoteLines struct {
	QuoteLine QuoteLine `xml:"QuoteLine"`
}

type QuoteCharge struct {
	CostType           string                `xml:"CostType"`
	Charge             CurrencyValueWithBase `xml:"Charge"`
	DiscountPercent    string                `xml:"DiscountPercent"`
	Discount           CurrencyValueWithBase `xml:"Discount"`
	NetCharge          CurrencyValueWithBase `xml:"NetCharge"`
	NetChargeOperating CurrencyValueWithBase `xml:"NetChargeOperating"`
	AccessorialQty     string                `xml:"AccessorialQty"`
}

type QuoteCharges struct {
	QuoteCharge QuoteCharge `xml:"QuoteCharge"`
}

type Quote struct {
	OrganizationName                 string         `xml:"OrganizationName"`
	QuoteNum                         string         `xml:"QuoteNum"`
	Client                           TradingPartner `xml:"Client"`
	OriginCityName                   string         `xml:"OriginCityName"`
	OriginCountryISO2                string         `xml:"OriginCountryISO2"`
	OriginPostalCode                 string         `xml:"OriginPostalCode"`
	OriginStateCode                  string         `xml:"OriginStateCode"`
	OriginISOCountrySubdivision      string         `xml:"OriginISOCountrySubdivision"`
	DestinationCityName              string         `xml:"DestinationCityName"`
	DestinationCountryISO2           string         `xml:"DestinationCountryISO2"`
	DestinationPostalCode            string         `xml:"DestinationPostalCode"`
	DestinationStateCode             string         `xml:"DestinationStateCode"`
	DestinationISOCountrySubdivision string         `xml:"DestinationISOCountrySubdivision"`
	IsHazmat                         string         `xml:"IsHazmat"`
	PickupDate                       string         `xml:"Pickup,omitempty"`
	DeliveryDate                     string         `xml:"Delivery,omitempty"`
	QuoteLines                       QuoteLines     `xml:"QuoteLines"`
	QuoteCharges                     QuoteCharges   `xml:"QuoteCharges"`
}

type Quotes struct {
	Quote Quote `xml:"Quote"`
}

type QuoteBatchInfo struct {
	BatchDateTime string `xml:"BatchDateTime"`
	SentBy        string `xml:"SentBy"`
	PageNum       string `xml:"PageNum"`
	PageCnt       string `xml:"PageCnt"`
}

type QuoteData struct {
	XMLName   xml.Name       `xml:"ns2:QuoteData"`
	XMLNS     string         `xml:"xmlns:ns2,attr"`
	BatchInfo QuoteBatchInfo `xml:"BatchInfo"`
	Quotes    Quotes         `xml:"Quotes"`
}

// =============================================================================
// QUOTE SUBMISSION
// =============================================================================

type CreateQuoteResponse struct {
	XMLName              xml.Name                  `xml:"http://schemas.3gtms.com/tms/v1/tns QuoteData"`
	ImportQuotesResponse CreateQuoteImportResponse `xml:"ImportQuotesResponse"`
}

type CreateQuoteImportResponse struct {
	Result         string                    `xml:"Result"`
	ImportEntities CreateQuoteImportEntities `xml:"ImportEntities"`
}

type CreateQuoteImportEntities struct {
	SuccessCount int                     `xml:"SuccessCount"`
	FailureCount int                     `xml:"FailureCount"`
	WarningCount int                     `xml:"WarningCount"`
	ImportEntity CreateQuoteImportEntity `xml:"ImportEntity"`
}

type CreateQuoteImportEntity struct {
	ExternalKey string      `xml:"ExternalKey"`
	InternalID  string      `xml:"InternalId"`
	ImportError ImportError `xml:"ImportError"`
}

type ImportError struct {
	Message string `xml:"Message"`
}

// =============================================================================
// MISC
// =============================================================================

type Note struct {
	Note      string `xml:"note"`
	CreatedAt string `xml:"createdAt"`
	UpdatedBy string `xml:"updatedBy"`
	Source    string `xml:"source"`
}
