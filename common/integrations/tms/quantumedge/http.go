package quantumedge

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/log"
)

func (q *QuantumEdge) postWithHeaders(ctx context.Context, url string, body io.Reader,
	headers map[string]string) ([]byte, error) {
	return q.do(ctx, http.MethodPost, url, body, headers)
}

func (q *QuantumEdge) get(ctx context.Context, url string) ([]byte, error) {
	return q.getWithHeaders(ctx, url, map[string]string{})
}

func (q *QuantumEdge) getWithHeaders(ctx context.Context, url string, headers map[string]string) ([]byte, error) {
	return q.do(ctx, http.MethodGet, url, nil, headers)
}

func (q *QuantumEdge) do(ctx context.Context, method string, url string,
	body io.Reader, headers map[string]string) ([]byte, error) {
	ctx = log.With(ctx, zap.String("tenant", q.config.Tenant), zap.Uint("tmsID", q.tms.ID))

	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create %s request for QuantumEdge: %w", method, err)
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	resp, err := q.httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, q.tms, err)
		return nil, fmt.Errorf("could not send %s request for QuantumEdge: %w", method, err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Check for response errors in body
	err = q.respToHTTPResponseError(req, resp, respBody)

	httplog.LogHTTPResponseCode(ctx, q.tms, resp.StatusCode)

	return respBody, err
}

// QuantumEdge returns 200s with error messages in HTML body (aljex all over again haha)
func (q *QuantumEdge) respToHTTPResponseError(req *http.Request, resp *http.Response, body []byte) error {
	if resp.StatusCode >= 300 {
		return errtypes.NewHTTPResponseError(q.tms, req, resp, body)
	}

	htmlBody := strings.ToLower(string(body))

	if strings.Contains(htmlBody, "your login information is not valid") ||
		strings.Contains(htmlBody, "please log in") ||
		strings.Contains(htmlBody, "session expired") {

		resp.StatusCode = http.StatusUnauthorized

		return errtypes.NewHTTPResponseError(q.tms, req, resp, body)
	}

	return nil
}

func (q *QuantumEdge) postFormURLEncoded(ctx context.Context, apiURL string,
	formData map[string]string) ([]byte, error) {
	headers := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
		"Accept":       "application/json, text/javascript, */*; q=0.01",
	}

	data := make([]string, 0, len(formData))
	for key, value := range formData {
		data = append(data, fmt.Sprintf("%s=%s", url.QueryEscape(key), url.QueryEscape(value)))
	}
	bodyStr := strings.Join(data, "&")

	return q.postWithHeaders(ctx, apiURL, strings.NewReader(bodyStr), headers)
}
