package quantumedge

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (q *QuantumEdge) buildAdditionalReferences(loadData *LoadData) models.AdditionalReferences {
	var refs models.AdditionalReferences

	if loadData.BOLNum != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "BOL",
			Number:    loadData.BOLNum,
		})
	}

	if loadData.ContainerNumber != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "CONTAINER",
			Number:    loadData.ContainerNumber,
		})
	}

	if loadData.SealNumber != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "SEAL",
			Number:    loadData.SealNumber,
		})
	}

	if loadData.ChassisNumber != "" {
		refs = append(refs, models.AdditionalReference{
			Qualifier: "CHASSIS",
			Number:    loadData.ChassisNumber,
		})
	}

	return refs
}

func mapAdditionalReferenceQualifierToField(qualifier string) (string, bool) {
	qualifierUpper := strings.ToUpper(strings.TrimSpace(qualifier))

	fieldMap := map[string]string{
		"PO":        "customerPoNum",
		"BOL":       "bolNum",
		"CONTAINER": "imdlContainerNumber",
		"SEAL":      "sealNumber",
		"CHASSIS":   "chassisNumber",
	}

	field, ok := fieldMap[qualifierUpper]
	return field, ok
}

func (q *QuantumEdge) updateCustomerReference(ctx context.Context, shipmentID, refNumber string) error {
	updates := map[string]string{
		"customerReference": refNumber,
	}

	updatesJSON, err := json.Marshal(updates)
	if err != nil {
		return fmt.Errorf("failed to marshal customer reference update: %w", err)
	}

	formData := map[string]string{
		"shipmentID": shipmentID,
		"updates":    string(updatesJSON),
	}

	apiURL := fmt.Sprintf("https://%s.edgetms.com/cfc/shipments/ShipmentAPI.cfc?method=update", q.config.Tenant)
	respBody, err := q.postFormURLEncoded(ctx, apiURL, formData)
	if err != nil {
		return fmt.Errorf("failed to update customer reference: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		log.Warn(ctx, "failed to parse API response for customer reference, but continuing",
			zap.Error(err),
			zap.String("responseBody", string(respBody)))
		return nil
	}

	log.Info(ctx, "successfully updated customer reference",
		zap.String("shipmentID", shipmentID),
		zap.String("refNumber", refNumber))

	return nil
}

func (q *QuantumEdge) updateAdditionalReferences(ctx context.Context, shipmentID string,
	additionalRefs models.AdditionalReferences, poNums string) error {

	updates := make(map[string]string)

	if strings.TrimSpace(poNums) != "" {
		updates["customerPoNum"] = poNums
	}

	for _, ref := range additionalRefs {
		fieldName, ok := mapAdditionalReferenceQualifierToField(ref.Qualifier)
		if !ok {
			log.Warn(ctx, "unsupported additional reference qualifier, skipping",
				zap.String("qualifier", ref.Qualifier),
				zap.String("number", ref.Number))
			continue
		}

		if strings.TrimSpace(ref.Number) == "" {
			log.Debug(ctx, "skipping empty additional reference",
				zap.String("qualifier", ref.Qualifier))
			continue
		}

		updates[fieldName] = ref.Number
	}

	// If no valid updates, return early
	if len(updates) == 0 {
		log.Debug(ctx, "no valid additional references to update")
		return nil
	}

	updatesJSON, err := json.Marshal(updates)
	if err != nil {
		return fmt.Errorf("failed to marshal updates: %w", err)
	}

	formData := map[string]string{
		"shipmentID": shipmentID,
		"updates":    string(updatesJSON),
	}

	apiURL := fmt.Sprintf("https://%s.edgetms.com/cfc/shipments/ShipmentAPI.cfc?method=update", q.config.Tenant)
	respBody, err := q.postFormURLEncoded(ctx, apiURL, formData)
	if err != nil {
		return fmt.Errorf("failed to update additional references: %w", err)
	}

	var apiResp APIResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		log.Warn(ctx, "failed to parse API response, but continuing",
			zap.Error(err),
			zap.String("responseBody", string(respBody)))
		return nil
	}

	log.Info(ctx, "successfully updated additional reference fields",
		zap.String("shipmentID", shipmentID),
		zap.Int("fieldsUpdated", len(updates)),
		zap.Any("updates", updates))

	return nil
}
