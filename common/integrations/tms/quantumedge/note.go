package quantumedge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type notesResponse struct {
	Success bool `json:"success"`
	Data    struct {
		Notes []struct {
			ID         int    `json:"id"`
			Content    string `json:"content"`
			User       string `json:"user"`
			Type       string `json:"type"`
			LegSeq     any    `json:"legSeq"`
			Source     string `json:"source"`
			CreateDate string `json:"createDate"`
		} `json:"notes"`
	} `json:"data"`
}

func (q *QuantumEdge) getLoadNotes(ctx context.Context, shipmentID string) ([]Note, error) {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/cfc/shipments/notes/ShipmentNoteAPI.cfc", q.config.Tenant)
	params := url.Values{}
	params.Set("method", "getAllForShipment")
	params.Set("shipmentId", shipmentID)

	fullURL := baseURL + "?" + params.Encode()

	body, err := q.get(ctx, fullURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch load notes: %w", err)
	}

	var resp notesResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return nil, fmt.Errorf("failed to parse notes response: %w", err)
	}

	if !resp.Success {
		return nil, errors.New("failed to fetch notes: API returned unsuccessful response")
	}

	notes := make([]Note, 0, len(resp.Data.Notes))
	for _, n := range resp.Data.Notes {
		notes = append(notes, Note{
			ID:          fmt.Sprintf("%d", n.ID),
			Content:     n.Content,
			User:        n.User,
			Type:        n.Type,
			ShipmentLeg: n.LegSeq,
			Source:      n.Source,
			CreatedAt:   n.CreateDate,
		})
	}

	return notes, nil
}

// updateStopNote updates a note on a specific stop in QuantumEdge
func (q *QuantumEdge) updateStopNote(ctx context.Context, noteID, shipmentID, shipmentStopID string,
	stopTypeID int, noteContent string) error {
	baseURL := fmt.Sprintf("https://%s.edgetms.com/cfc/shipments/shipment_notes.cfc", q.config.Tenant)
	params := url.Values{}
	params.Set("method", "updateNoteOnStop")
	params.Set("returnFormat", "json")

	argCollection := fmt.Sprintf(
		`{"id":%s,"shipmentId":%s,"shipmentStopId":%s,"stopTypeId":%d,"note":"%s","confirmation_display":false}`,
		noteID, shipmentID, shipmentStopID, stopTypeID, noteContent)
	params.Set("argumentCollection", argCollection)

	fullURL := baseURL + "?" + params.Encode()

	body, err := q.get(ctx, fullURL)
	if err != nil {
		return fmt.Errorf("failed to update stop note: %w", err)
	}

	var resp NoteOperationResponse
	if err := json.Unmarshal(body, &resp); err != nil {
		return fmt.Errorf("failed to parse update note response: %w", err)
	}

	if !resp.Result {
		return fmt.Errorf("failed to update note: API returned result=false for note %s", noteID)
	}

	return nil
}

// parseNoteType extracts stop type and sequence from note type string
// Examples: "Pick-Up (1 of 2)" -> ("pickup", 1, true)
//
//	"Delivery (2 of 2)" -> ("delivery", 2, true)
//	"Pick-Up" -> ("pickup", 1, true)
//	"General Shipment Note" -> ("", 0, false)
func (q *QuantumEdge) parseNoteType(noteType string) (stopType string, sequence int, isStopNote bool) {
	// Check for pickup notes
	if strings.HasPrefix(noteType, "Pick-Up") {
		stopType = "pickup"
		isStopNote = true
		sequence = q.extractSequence(noteType)
		return
	}

	// Check for delivery notes
	if strings.HasPrefix(noteType, "Delivery") {
		stopType = "delivery"
		isStopNote = true
		sequence = q.extractSequence(noteType)
		return
	}

	// Not a stop note
	return "", 0, false
}

// extractSequence extracts the sequence number from note type like "Pick-Up (2 of 3)"
// Returns 1 if no sequence found (default to first stop)
func (q *QuantumEdge) extractSequence(noteType string) int {
	// Pattern: "Type (X of Y)" where X is the sequence number
	// Example: "Pick-Up (2 of 3)" -> extract "2"
	start := strings.Index(noteType, "(")
	if start == -1 {
		return 1 // No sequence, default to first stop
	}

	end := strings.Index(noteType, " of ")
	if end == -1 || end <= start {
		return 1
	}

	sequenceStr := strings.TrimSpace(noteType[start+1 : end])
	sequence, err := strconv.Atoi(sequenceStr)
	if err != nil || sequence < 1 {
		return 1
	}

	return sequence
}

// findStopByTypeAndSequence finds the Nth stop of a given type
// sequence is 1-based (1 = first stop of type, 2 = second stop of type, etc.)
func (q *QuantumEdge) findStopIndexByNote(stops []models.Stop, note models.Note) int {
	stopType, stopSequence, isStopNote := q.parseNoteType(note.Type)
	if !isStopNote {
		return -1
	}

	count := 0
	for i := range stops {
		if q.stopTypesMatch(stops[i].StopType, stopType) {
			count++
			if count == stopSequence {
				return i
			}
		}
	}

	return -1
}

// stopTypeToID converts stop type string to QE's stopTypeID (1=pickup, 2=delivery)
func (q *QuantumEdge) stopTypeToID(stopType string) int {
	if stopType == "delivery" || stopType == "dropoff" {
		return 2
	}
	return 1 // pickup
}

// stopTypesMatch checks if two stop types are equivalent
// Handles the mapping between Drumkit standard "dropoff" and QuantumEdge "delivery"
func (q *QuantumEdge) stopTypesMatch(type1, type2 string) bool {
	normalizeType := func(t string) string {
		if t == "dropoff" || t == "delivery" {
			return "delivery"
		}
		return t
	}
	return normalizeType(type1) == normalizeType(type2)
}

// populateStopNotes populates the ApptNote field for each stop by matching notes from the notes array
func (q *QuantumEdge) populateStopNotes(_ context.Context, stops *[]models.Stop, notes []Note) {
	if stops == nil || len(*stops) == 0 || len(notes) == 0 {
		return
	}

	for _, note := range notes {
		modelNote := models.Note{
			Type: note.Type,
		}

		stopIndex := q.findStopIndexByNote(*stops, modelNote)
		if stopIndex != -1 {
			(*stops)[stopIndex].ApptNote = note.Content
		}
	}
}

// findNoteIndexByStop finds the note that matches a stop by calculating stop sequence
// Returns -1 if no match found
func (q *QuantumEdge) findNoteIndexByStop(notes []models.Note, stop models.Stop, stops []models.Stop) int {
	// Calculate this stop's sequence number within its type
	sequence := 0
	for _, s := range stops {
		if q.stopTypesMatch(s.StopType, stop.StopType) {
			sequence++
			if s.StopNumber == stop.StopNumber {
				break
			}
		}
	}

	if sequence == 0 {
		return -1
	}

	// Find note with matching type and sequence
	for i := range notes {
		noteStopType, noteSequence, isStopNote := q.parseNoteType(notes[i].Type)
		if !isStopNote {
			continue
		}
		if q.stopTypesMatch(noteStopType, stop.StopType) && noteSequence == sequence {
			return i
		}
	}

	return -1
}

// updateStopNoteFromStop updates a stop note when the ApptNote field is modified
func (q *QuantumEdge) updateStopNoteFromStop(ctx context.Context, reqLoad *models.Load, stop models.Stop) error {
	// Find matching note in notes array
	noteIndex := q.findNoteIndexByStop(reqLoad.Notes, stop, reqLoad.Stops)
	if noteIndex == -1 {
		log.Warn(ctx, "no matching note found for stop ApptNote update",
			zap.String("stopType", stop.StopType),
			zap.Int("stopNumber", stop.StopNumber))
		return nil
	}

	matchingNote := &reqLoad.Notes[noteIndex]
	noteID := fmt.Sprintf("%v", matchingNote.ExternalTMSID)
	if noteID == "" || noteID == "<nil>" {
		return fmt.Errorf("note missing ExternalTMSID for stop %d", stop.StopNumber)
	}

	// Sync ApptNote content back to the notes array before updating API
	reqLoad.Notes[noteIndex].Note = stop.ApptNote

	return q.updateStopNote(ctx, noteID, reqLoad.ExternalTMSID,
		stop.ExternalTMSStopID, q.stopTypeToID(stop.StopType), stop.ApptNote)
}
