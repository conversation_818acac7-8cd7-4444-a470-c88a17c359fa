package quantumedge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type QuantumEdge struct {
	tms        models.Integration
	config     *Config
	httpClient httpClient
	userID     string
}

type httpClient interface {
	Do(req *http.Request) (*http.Response, error)
}

type Config struct {
	Username string
	Password string
	Tenant   string
}

func New(ctx context.Context, tms models.Integration) (*QuantumEdge, error) {
	ctx = log.With(ctx, zap.String("tenant", tms.Tenant), zap.Uint("tmsID", tms.ID))

	cachedClient, err := retrieveRedisClient(ctx, tms.ServiceID, tms.ID)
	if err != nil {
		return nil, err
	}

	if cachedClient != nil {
		cachedClient.tms = tms
		log.Info(ctx, "re-using existing QuantumEdge client")
		return cachedClient, nil
	}

	quantumEdge, err := initialize(ctx, tms)
	if err != nil {
		return nil, err
	}

	_, err = quantumEdge.Auth(ctx)
	if err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	userID, err := quantumEdge.getUserID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user ID: %w", err)
	}
	quantumEdge.userID = userID

	log.Info(ctx, "fetched user ID", zap.String("userID", userID))

	quantumEdge.dumpRedisClient(ctx)

	log.Info(ctx, "successfully authenticated QuantumEdge client")

	return quantumEdge, nil
}

func initialize(ctx context.Context, tms models.Integration) (*QuantumEdge, error) {
	password, err := crypto.DecryptAESGCM(ctx, string(tms.EncryptedPassword), nil)
	if err != nil {
		return nil, fmt.Errorf("error decrypting password: %w", err)
	}

	if tms.Username == "" || password == "" {
		return nil, errors.New("missing QuantumEdge username or password")
	}

	config := &Config{
		Username: tms.Username,
		Password: password,
		Tenant:   tms.Tenant,
	}

	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		return nil, fmt.Errorf("could not create QuantumEdge cookie jar: %w", err)
	}

	httpClient := otel.TracingHTTPClient(120 * time.Second)
	httpClient.Jar = cookieJar

	quantumEdge := QuantumEdge{
		tms:        tms,
		httpClient: httpClient,
		config:     config,
	}

	return &quantumEdge, nil
}

func (q *QuantumEdge) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {

	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnboardQuantumEdge", otel.IntegrationAttrs(q.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("missing QuantumEdge username or password")
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, onboardRequest.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		EncryptedPassword: encryptedPassword,
		Username:          onboardRequest.Username,
		Tenant:            onboardRequest.Tenant,
	}, nil
}

func (q *QuantumEdge) GetTestLoads() map[string]bool {
	isRoar := strings.Contains(q.tms.Tenant, "roar")
	return map[string]bool{
		"1398878": isRoar,
	}
}

func (q *QuantumEdge) GetDefaultLoadAttributes() models.LoadAttributes {
	return models.LoadAttributes{}
}

func (q *QuantumEdge) CreateLoad(context.Context, models.Load, *models.TMSUser) (models.Load, error) {
	return models.Load{}, helpers.NotImplemented(models.QuantumEdge, "CreateLoad")
}

// formatDateForQuantumEdge converts a time.Time to QuantumEdge's date format: "MM/DD/YY - MM/DD/YY"
func formatDateForQuantumEdge(t time.Time) string {
	formatted := t.Format("01/02/06")
	return formatted + " - " + formatted
}

// buildFilters creates the filters JSON for date-based queries using >= (for now) operator on FromDate
func buildFilters(query models.SearchLoadsQuery) (string, error) {
	if !query.FromDate.Valid {
		return `{"rules":[]}`, nil
	}

	filters := Filters{
		GroupOp: "AND",
		Rules: []FilterRule{
			{
				Op:      "ge",
				OpValue: "&gt;=",
				Data:    formatDateForQuantumEdge(query.FromDate.Time),
				Field:   "create_date_formatedDate",
			},
		},
	}

	jsonBytes, err := json.Marshal(filters)
	if err != nil {
		return "", fmt.Errorf("failed to marshal filters: %w", err)
	}

	return string(jsonBytes), nil
}

func (q *QuantumEdge) GetLoadIDs(ctx context.Context, query models.SearchLoadsQuery) ([]string, error) {
	var err error
	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadIDsQuantumEdge", otel.IntegrationAttrs(q.tms))
	defer func() { metaSpan.End(err) }()

	log.Info(ctx, "quantumedge.GetLoadIDs",
		zap.Bool("hasFromDate", query.FromDate.Valid),
		zap.Bool("hasToDate", query.ToDate.Valid))

	filtersJSON, err := buildFilters(query)
	if err != nil {
		return nil, fmt.Errorf("failed to build filters: %w", err)
	}

	hasFilters := query.FromDate.Valid

	url := fmt.Sprintf("https://%s.edgetms.com/cfc/reporting/ReportingAPI.cfc?method=getGridData",
		q.config.Tenant)

	// Fetch page 1 first to get the total number of pages
	formData := make(map[string]string)
	formData["datasourceID"] = "13"
	formData["savedSearchID"] = "0"
	formData["userID"] = q.userID
	formData["subFilterID"] = "0"
	formData["subFilterValue"] = ""
	formData["shouldSaveFilters"] = "true"
	formData["searchWithoutFilters"] = "true"
	formData["isFirstLoad"] = strconv.FormatBool(!hasFilters)
	formData["filterKey"] = ""
	formData["_search"] = strconv.FormatBool(hasFilters)
	formData["nd"] = fmt.Sprintf("%d", time.Now().UnixMilli())
	formData["rows"] = "100"
	formData["page"] = "1"
	formData["sidx"] = ""
	formData["sord"] = ""
	formData["filters"] = filtersJSON

	respBody, err := q.postFormURLEncoded(ctx, url, formData)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch grid data (page 1): %w", err)
	}

	var gridResp GridDataResponse
	if err := json.Unmarshal(respBody, &gridResp); err != nil {
		return nil, fmt.Errorf("failed to parse grid data response (page 1): %w", err)
	}

	totalPages, err := strconv.Atoi(gridResp.Total)
	if err != nil {
		return nil, fmt.Errorf("failed to parse total pages: %w", err)
	}

	log.Info(ctx, "fetched grid metadata",
		zap.Int("totalPages", totalPages),
		zap.String("totalRecords", gridResp.Records))

	var loadIDs []string

	// Process page 1 (already fetched)
	// so we fetch from first page explicitly
	// to get access to the "total" field
	// and use that to paginate
	loadIDs = append(loadIDs, q.extractShipmentIDsFromRows(ctx, gridResp.Rows)...)
	log.Info(ctx, "extracted IDs from page 1", zap.Int("count", len(loadIDs)))

	// Fetch remaining pages
	for page := 2; page <= totalPages; page++ {
		formData["page"] = strconv.Itoa(page)

		respBody, err := q.postFormURLEncoded(ctx, url, formData)
		if err != nil {
			log.Warn(ctx, "failed to fetch grid data for page",
				zap.Int("page", page),
				zap.Error(err))
			continue
		}

		var pageResp GridDataResponse
		if err := json.Unmarshal(respBody, &pageResp); err != nil {
			log.Warn(ctx, "failed to parse grid data response for page",
				zap.Int("page", page),
				zap.Error(err))
			continue
		}

		pageIDs := q.extractShipmentIDsFromRows(ctx, pageResp.Rows)
		loadIDs = append(loadIDs, pageIDs...)
		log.Info(ctx, "extracted IDs from page",
			zap.Int("page", page),
			zap.Int("pageCount", len(pageIDs)),
			zap.Int("totalCount", len(loadIDs)))
	}

	log.Info(ctx, "finished fetching all pages",
		zap.Int("totalPages", totalPages),
		zap.Int("totalLoadIDs", len(loadIDs)))

	return loadIDs, nil
}

// getUserID fetches the authenticated user's ID from QuantumEdge
func (q *QuantumEdge) getUserID(ctx context.Context) (string, error) {
	url := fmt.Sprintf("https://%s.edgetms.com/cfc/users/UserAPI.cfc?method=get&_=%d",
		q.config.Tenant, time.Now().UnixMilli())

	respBody, err := q.get(ctx, url)
	if err != nil {
		return "", fmt.Errorf("failed to fetch user ID: %w", err)
	}

	var userResp UserResponse
	if err := json.Unmarshal(respBody, &userResp); err != nil {
		return "", fmt.Errorf("failed to parse user response: %w", err)
	}

	if !userResp.Success {
		return "", fmt.Errorf("user API request failed: %s", userResp.Message)
	}

	if userResp.Data.User.ID == 0 {
		return "", errors.New("user ID not found in response")
	}

	return strconv.Itoa(userResp.Data.User.ID), nil
}

// extractShipmentIDsFromRows extracts shipment IDs from grid rows
func (q *QuantumEdge) extractShipmentIDsFromRows(ctx context.Context, rows [][]any) []string {
	var shipmentIDs []string

	for _, row := range rows {
		if len(row) < 3 {
			continue
		}

		shipmentIDRaw := row[2]

		var shipmentID string
		switch v := shipmentIDRaw.(type) {
		case string:
			shipmentID = q.extractShipmentIDFromHTML(v)
		case float64:
			shipmentID = fmt.Sprintf("%.0f", v)
		default:
			log.Warn(ctx, "unexpected shipment ID type",
				zap.Any("value", shipmentIDRaw),
				zap.String("type", fmt.Sprintf("%T", shipmentIDRaw)))
			continue
		}

		if shipmentID != "" {
			shipmentIDs = append(shipmentIDs, shipmentID)
		}
	}

	return shipmentIDs
}

func (q *QuantumEdge) GetLoadsByIDType(
	ctx context.Context,
	id string,
	idType string,
) ([]models.Load, models.LoadAttributes, error) {
	var err error
	spanAttrs := append(
		otel.IntegrationAttrs(q.tms),
		attribute.String("id", id), attribute.String("idType", idType),
	)

	ctx, metaSpan := otel.StartSpan(ctx, "GetLoadsByIDTypeQuantumEdge", spanAttrs)
	defer func() { metaSpan.End(err) }()

	switch idType {
	case FreightTrackingIDType:
		load, attrs, err := q.GetLoad(ctx, id)
		if err != nil {
			return nil, models.LoadAttributes{}, err
		}
		return []models.Load{load}, attrs, nil

	case RefNumberIDType:
		// For now (temp), return not found (DB-only lookup will handle this)
		return nil, q.GetDefaultLoadAttributes(), errtypes.EntityNotFoundError(q.tms, id, "RefNumber")

	default:
		return nil, q.GetDefaultLoadAttributes(),
			helpers.NotImplemented(q.tms.Name, "GetLoadsByIDType with idType: "+idType)
	}
}

func (q *QuantumEdge) PostCheckCall(context.Context, *models.Load, models.CheckCall) error {
	return helpers.NotImplemented(models.QuantumEdge, "PostCheckCall")
}

func (q *QuantumEdge) GetCheckCallsHistory(context.Context, uint, string) ([]models.CheckCall, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "GetCheckCallsHistory")
}

func (q *QuantumEdge) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.QuantumEdge, "PostException")
}

func (q *QuantumEdge) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "GetExceptionHistory")
}

func (q *QuantumEdge) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "PostNote")
}

func (q *QuantumEdge) GetCustomers(context.Context) ([]models.TMSCustomer, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "GetCustomers")
}

func (q *QuantumEdge) GetUsers(context.Context) ([]models.TMSUser, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "GetUsers")
}

func (q *QuantumEdge) GetLocations(context.Context, ...models.TMSOption) ([]models.TMSLocation, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "GetLocations")
}

func (q *QuantumEdge) CreateLocation(
	context.Context,
	models.CreateLocationRequest,
	*models.TMSUser,
) (models.TMSLocation, error) {
	return models.TMSLocation{}, helpers.NotImplemented(models.QuantumEdge, "CreateLocation")
}

func (q *QuantumEdge) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "GetCarriers")
}

func (q *QuantumEdge) CreateQuote(context.Context, models.CreateQuoteBody) (*models.CreateQuoteResponse, error) {
	return nil, helpers.NotImplemented(models.QuantumEdge, "CreateQuote")
}

func (q *QuantumEdge) GetOrder(context.Context, string) (*models.Order, models.OrderAttributes, error) {
	return nil, models.OrderAttributes{}, helpers.NotImplemented(models.QuantumEdge, "GetOrder")
}

func (q *QuantumEdge) MapTransportTypeEnum(string) (models.TransportType, error) {
	return "", helpers.NotImplemented(models.QuantumEdge, "MapTransportTypeEnum")
}

// extractShipmentIDFromHTML extracts the shipment ID from HTML markup
// Example input: "<a href=\"/index.cfm?display_menu=0&action=shipment_main&shipment_id=1398879\" " +
//
//	"target=\"_blank\">1398879"
//
// or "<input type=\"button\" .../><a href=\"...\">1398879"
func (q *QuantumEdge) extractShipmentIDFromHTML(html string) string {
	// first try to extract from the href parameter
	re := regexp.MustCompile(`shipment_id=(\d+)`)
	if matches := re.FindStringSubmatch(html); len(matches) > 1 {
		return matches[1]
	}

	// fallbackk: try to extract from the visible text after the last >
	// This handles cases like: ">1398879" or ">1398879</a>"
	lastGT := strings.LastIndex(html, ">")
	if lastGT >= 0 && lastGT < len(html)-1 {
		afterGT := html[lastGT+1:]
		// Extract just the numeric part
		numRe := regexp.MustCompile(`^\d+`)
		if matches := numRe.FindString(afterGT); matches != "" {
			return matches
		}
	}

	// last resort: try to find any number in the string
	re = regexp.MustCompile(`\d+`)
	if matches := re.FindString(html); matches != "" {
		return matches
	}

	return ""
}
