package quantumedge

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

type SerializableCookie struct {
	Name    string
	Value   string
	Path    string
	Domain  string
	Expires time.Time
}

type SerializableQuantumEdge struct {
	Config        *Config
	Cookies       []SerializableCookie
	IntegrationID uint
	ServiceID     uint
	UserID        string
}

func FromHTTP(cookies []*http.Cookie) []SerializableCookie {
	var sc []SerializableCookie
	for _, c := range cookies {
		sc = append(sc, SerializableCookie{
			Name:    c.Name,
			Value:   c.Value,
			Path:    c.Path,
			Domain:  c.Domain,
			Expires: c.Expires,
		})
	}
	return sc
}

func (s *SerializableQuantumEdge) ToHTTP() []*http.Cookie {
	var cookies []*http.Cookie
	for _, c := range s.Cookies {
		cookies = append(cookies, &http.Cookie{
			Name:    c.Name,
			Value:   c.Value,
			Path:    c.Path,
			Domain:  c.Domain,
			Expires: c.Expires,
		})
	}
	return cookies
}

func (s *SerializableQuantumEdge) Marshal() ([]byte, error) {
	return json.Marshal(s)
}

func Unmarshal(data []byte) (*SerializableQuantumEdge, error) {
	var s SerializableQuantumEdge
	err := json.Unmarshal(data, &s)
	return &s, err
}

func NewQuantumEdgeFromSerializable(ctx context.Context, sq SerializableQuantumEdge) (*QuantumEdge, error) {
	cookies := sq.ToHTTP()

	jar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		return nil, fmt.Errorf("could not create cookie jar: %w", err)
	}

	client := otel.TracingHTTPClient(120 * time.Second)
	client.Jar = jar

	integration := models.Integration{
		ServiceID: sq.ServiceID,
		Name:      models.QuantumEdge,
		Type:      models.TMS,
	}
	integration.ID = sq.IntegrationID

	q := &QuantumEdge{
		httpClient: client,
		config:     sq.Config,
		tms:        integration,
		userID:     sq.UserID,
	}

	if len(cookies) > 0 {
		u, err := url.Parse(fmt.Sprintf("https://%s.edgetms.com", q.config.Tenant))
		if err != nil {
			return nil, fmt.Errorf("failed to parse URL: %w", err)
		}

		jar.SetCookies(u, cookies)
		log.Info(
			ctx,
			"restored cookies to QuantumEdge cookiejar",
			zap.Int("cookieCount", len(cookies)))
	}

	return q, nil
}

func redisClientKey(serviceID uint, tmsID uint) string {
	return fmt.Sprintf("service-%d-tms-%d-quantumedge", serviceID, tmsID)
}

func retrieveRedisClient(ctx context.Context, serviceID uint, tmsID uint) (*QuantumEdge, error) {
	sq, found, err := redis.GetKey[SerializableQuantumEdge](ctx, redisClientKey(serviceID, tmsID))
	if err != nil && !errors.Is(err, redis.NilEntry) {
		log.Warn(ctx, "failed to get QuantumEdge session from Redis", zap.Error(err))
	}

	if found {
		client, err := NewQuantumEdgeFromSerializable(ctx, sq)
		if err != nil {
			log.Warn(ctx, "failed to unmarshal QuantumEdge session from Redis", zap.Error(err))
			return nil, err
		}
		if client != nil {
			log.Info(ctx, "re-using existing QuantumEdge client")
		}
		return client, nil
	}

	return nil, nil
}

func (q *QuantumEdge) MarshalQuantumEdge() (string, error) {
	var cookies []*http.Cookie

	if client, ok := q.httpClient.(*http.Client); ok && client.Jar != nil {
		u, err := url.Parse(fmt.Sprintf("https://%s.edgetms.com", q.config.Tenant))
		if err != nil {
			return "", fmt.Errorf("failed to parse URL: %w", err)
		}
		cookies = client.Jar.Cookies(u)
	}

	sc := FromHTTP(cookies)
	sq := SerializableQuantumEdge{
		Config:  q.config,
		Cookies: sc,
	}
	bytes, err := json.Marshal(sq)
	return string(bytes), err
}

func (q *QuantumEdge) dumpRedisClient(ctx context.Context) {
	client, ok := q.httpClient.(*http.Client)
	if !ok {
		//nolint:lll
		log.Warn(ctx, "failed to cache QuantumEdge client in Redis: http client is not a *http.Client, this should only happen in a unit test")
		return
	}

	u, err := url.Parse(fmt.Sprintf("https://%s.edgetms.com", q.config.Tenant))
	if err != nil {
		log.Warn(ctx, "failed to cache QuantumEdge client in Redis: failed to parse URL", zap.Error(err))
		return
	}

	sq := SerializableQuantumEdge{
		Config:        q.config,
		Cookies:       make([]SerializableCookie, len(client.Jar.Cookies(u))),
		IntegrationID: q.tms.ID,
		ServiceID:     q.tms.ServiceID,
		UserID:        q.userID,
	}

	for i, cookie := range client.Jar.Cookies(u) {
		sq.Cookies[i] = SerializableCookie{
			Name:    cookie.Name,
			Value:   cookie.Value,
			Path:    cookie.Path,
			Domain:  cookie.Domain,
			Expires: cookie.Expires,
		}
	}

	redisKey := redisClientKey(q.tms.ServiceID, q.tms.ID)
	// JWT in cookie expires in 4 hours, we proactively refresh after 3 hours
	err = redis.SetKey(ctx, redisKey, sq, 3*time.Hour)
	if err != nil {
		log.Warn(ctx, "failed to set QuantumEdge session in Redis", zap.Error(err))
	}
}
