package quantumedge

// QuantumEdgeLoadData represents the raw load data extracted from QuantumEdge
type LoadData struct {
	// Basic shipment fields
	ShipmentID      string
	CustomerPONum   string
	CustomerRef     string
	BOLNum          string
	SealNumber      string
	ContainerNumber string
	ChassisNumber   string
	ContainerPrefix string
	ContainerSuffix string
	CommodityDesc   string
	ShipmentValue   string

	// Weight and dimensions
	Weight               string
	WeightType           string
	EmptyContainerWeight string
	Length               string
	Width                string
	Height               string
	DimensionUnit        string

	// Equipment and temperature
	EquipmentType   string
	EquipmentLength string
	Temperature     string
	TemperatureUnit string

	// Mode and service
	Mode        string
	ServiceType string

	// Organizational fields
	AccountManager string
	Office         string
	Team           string
	SalesRep       string

	// Status and dates
	Status       string
	CreatedDate  string
	ModifiedDate string

	// Additional references
	TrailerNumber string
	TractorNumber string
	DriverName    string
	CarrierName   string

	// Location information
	CustomerInfo  CustomerInfo
	PickupInfo    LocationInfo
	ConsigneeInfo LocationInfo

	// Parsed stops from stops API
	ParsedStops []LocationInfo

	Notes []Note

	// Rate data
	RateData *RateData
}

// CustomerInfo holds customer details
type CustomerInfo struct {
	Name    string
	Address string
	City    string
	State   string
	Zip     string
	Phone   string
	Contact string
}

// LocationInfo holds pickup/consignee/stop location details
type LocationInfo struct {
	Name            string
	Address         string
	AddressLine2    string
	City            string
	State           string
	Zip             string
	Country         string
	Phone           string
	Contact         string
	Email           string
	Date            string
	Time            string
	ReadyDate       string
	ReadyTime       string
	DeliveryDate    string
	DeliveryTime    string
	AppointmentReq  bool
	AppointmentType string
	Instructions    string
	RefNumber       string
	// Additional fields for stops parsing
	StopType    string // "pickup" or "delivery"
	StopNumber  int    // Sequential stop number
	StopID      string // External TMS stop ID
	SiteID      string // QuantumEdge site ID for the location. This is needed to update stops
	ServiceType string // "Live" or "Drop"
	// Schedule appointment fields
	SchedFromDate string
	SchedFromTime string
	SchedToDate   string
	SchedToTime   string
	// Driver arrival/departure fields
	DriverArrivedDate  string
	DriverArrivedTime  string
	DriverDepartedDate string
	DriverDepartedTime string
}

type Note struct {
	ID          string
	Content     string
	User        string
	Type        string
	ShipmentLeg any
	Source      string
	CreatedAt   string
}

// RateData holds AR and AP rate information
type RateData struct {
	ARInvoices []ARInvoiceData `json:"arInvoices"`
	APInvoices []APInvoiceData `json:"apInvoices"`
}

// ARInvoiceData represents customer (AR) invoice data from QuantumEdge
type ARInvoiceData struct {
	TotalRowCount int                  `json:"TOTALROWCOUNT"`
	Query         ARInvoiceQueryResult `json:"QUERY"`
}

// ARInvoiceQueryResult holds the query result structure for AR invoices
type ARInvoiceQueryResult struct {
	Columns []string `json:"COLUMNS"`
	Data    [][]any  `json:"DATA"`
}

// APInvoiceData represents carrier (AP) invoice data from QuantumEdge
type APInvoiceData struct {
	TotalRowCount int                  `json:"TOTALROWCOUNT"`
	Query         APInvoiceQueryResult `json:"QUERY"`
}

// APInvoiceQueryResult holds the query result structure for AP invoices
type APInvoiceQueryResult struct {
	Columns []string `json:"COLUMNS"`
	Data    [][]any  `json:"DATA"`
}

// ARLineItem represents a parsed line item from AR invoice
type ARLineItem struct {
	LineItem            int     `json:"lineItem"`
	LineTypeID          int     `json:"lineTypeID"`
	LineTypeDesc        string  `json:"lineTypeDesc"`
	Amount              float64 `json:"amount"`
	AmountExch          float64 `json:"amountExch"`
	EnterDate           string  `json:"enterDate"`
	EnterUser           int     `json:"enterUser"`
	EnterUserName       string  `json:"enterUserName"`
	ReferenceNumber     string  `json:"referenceNumber"`
	Note                string  `json:"note"`
	UnitQuantity        float64 `json:"unitQuantity"`
	UnitPrice           float64 `json:"unitPrice"`
	UnitTypeID          int     `json:"unitTypeID"`
	UnitTypeName        string  `json:"unitTypeName"`
	RequiresCalculation int     `json:"requiresCalculation"`
}

// APLineItem represents a parsed line item from AP invoice
type APLineItem struct {
	LineItem        int     `json:"lineItem"`
	LineTypeID      int     `json:"lineTypeID"`
	LineTypeDesc    string  `json:"lineTypeDesc"`
	Amount          float64 `json:"amount"`
	AmountExch      float64 `json:"amountExch"`
	UnitQuantity    float64 `json:"unitQuantity"`
	UnitPrice       float64 `json:"unitPrice"`
	UnitTypeID      int     `json:"unitTypeID"`
	UnitTypeName    string  `json:"unitTypeName"`
	EnterDate       string  `json:"enterDate"`
	EnterUser       int     `json:"enterUser"`
	EnterUserName   string  `json:"enterUserName"`
	ReferenceNumber string  `json:"referenceNumber"`
	Note            string  `json:"note"`
	Sequence        int     `json:"sequence"`
	HeaderSite      string  `json:"headerSite"`
}

// APIResponse represents the standard API response format from QuantumEdge
type APIResponse struct {
	Message    string         `json:"message"`
	Success    bool           `json:"success"`
	DevMessage string         `json:"dev_message"`
	Errors     []string       `json:"errors"`
	Data       map[string]any `json:"data"`
}

// StopUpdatePayload represents the payload structure for updating a stop in QuantumEdge
type StopUpdatePayload struct {
	ShipmentID             int    `json:"shipmentId,omitempty"`
	StopSequence           int    `json:"stopSequence,omitempty"`
	StopTypeID             int    `json:"stopTypeId,omitempty"`
	ServiceTypeID          int    `json:"serviceTypeId,omitempty"`
	PrimaryReferenceNumber string `json:"primaryReferenceNumber,omitempty"`
	ReqFromDate            string `json:"reqFromDate,omitempty"`
	ReqToDate              string `json:"reqToDate,omitempty"`
	SchedFromDate          string `json:"schedFromDate,omitempty"`
	SchedToDate            string `json:"schedToDate,omitempty"`
	LiveStartDate          string `json:"liveStartDate,omitempty"`
	EqSpotDate             string `json:"eqSpotDate,omitempty"`
	NotifyReadyDate        string `json:"notifyReadyDate,omitempty"`
	CarrierNotifyReadyDate string `json:"carrierNotifyReadyDate,omitempty"`
	DepartDate             string `json:"departDate,omitempty"`
	PreventFutureDate      bool   `json:"preventFutureDate,omitempty"`
	LfdOrigin              string `json:"lfdOrigin,omitempty"`
	LfdDestination         string `json:"lfdDestination,omitempty"`
	ApptReqDate            string `json:"apptReqDate,omitempty"`
	CustReplyDate          string `json:"custReplyDate,omitempty"`
	IsBlind                bool   `json:"isBlind,omitempty"`
	BlindLabel             string `json:"blindLabel,omitempty"`
	SiteID                 string `json:"siteId,omitempty"`
	Comments               string `json:"comments,omitempty"`
	ApptNeeded             int    `json:"apptNeeded,omitempty"`
	SiteAlias              string `json:"siteAlias,omitempty"`
	PrimaryPhone           string `json:"primaryPhone,omitempty"`
	PrimaryPhoneExt        string `json:"primaryPhoneExt,omitempty"`
	HasLumper              string `json:"hasLumper,omitempty"`
	LumperAmount           int    `json:"lumperAmount,omitempty"`
	EmptyPulledDate        string `json:"emptyPulledDate,omitempty"`
	ConfirmationNumber     string `json:"confirmationNumber,omitempty"`
	RescheduleReasonID     int    `json:"rescheduleReasonId,omitempty"`
	LateReasonID           int    `json:"lateReasonId,omitempty"`
	LateReasonNote         string `json:"lateReasonNote,omitempty"`
	IsLateReasonPreCoded   int    `json:"isLateReasonPreCoded,omitempty"`
	ApptRequested          bool   `json:"apptRequested,omitempty"`
	ApptRequestedDate      string `json:"apptRequestedDate,omitempty"`
	SchedDropFromDate      string `json:"schedDropFromDate,omitempty"`
	SchedDropToDate        string `json:"schedDropToDate,omitempty"`
	RateConComments        string `json:"rateConComments,omitempty"`
}

// NoteOperationResponse represents the response from note update/delete operations
type NoteOperationResponse struct {
	StopTypeID     int  `json:"STOPTYPEID"`
	ShipmentID     int  `json:"SHIPMENTID"`
	Result         bool `json:"RESULT"`
	ShipmentStopID int  `json:"SHIPMENTSTOPID"`
}

// GridDataResponse represents the response from the getGridData API endpoint
type GridDataResponse struct {
	Total    string    `json:"total"`
	Records  string    `json:"records"`
	Page     string    `json:"page"`
	Rows     [][]any   `json:"rows"`
	UserData *UserData `json:"userdata,omitempty"`
}

// UserData contains summary information in the grid response
type UserData struct {
	ShipmentIDDisplay string `json:"shipment_id_display"`
	Counter           string `json:"counter"`
}

// FilterRule represents a single filter rule in the jqGrid filters JSON
type FilterRule struct {
	Op      string `json:"op"`
	OpValue string `json:"opValue"`
	Data    string `json:"data"`
	Field   string `json:"field"`
}

// Filters represents the complete filters structure for jqGrid API
type Filters struct {
	GroupOp string       `json:"groupOp"`
	Rules   []FilterRule `json:"rules"`
}

// UserResponse represents the response from the UserAPI.cfc endpoint
type UserResponse struct {
	Message    string   `json:"message"`
	Success    bool     `json:"success"`
	DevMessage string   `json:"dev_message"`
	Errors     []string `json:"errors"`
	Data       struct {
		User User `json:"user"`
	} `json:"data"`
}

// User represents the authenticated user information from QuantumEdge
type User struct {
	ID          int      `json:"id"`
	FirstName   string   `json:"firstName"`
	LastName    string   `json:"lastName"`
	FullName    string   `json:"fullName"`
	Email       string   `json:"email"`
	PhoneNumber string   `json:"phoneNumber"`
	PhoneExt    string   `json:"phoneExt"`
	JobTitle    string   `json:"jobTitle"`
	Type        string   `json:"type"`
	Client      string   `json:"client"`
	OfficeID    int      `json:"officeId"`
	Roles       []string `json:"roles"`
}
