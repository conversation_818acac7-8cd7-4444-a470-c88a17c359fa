# Mcleod Enterprise

## Setup

- To access our Remote Session via Windows365, see [here](https://www.notion.so/axleapi/Beacon-Integrations-cec4a355d8294e10a471b5f3cd62355e?p=1052b16b087a80098bdfe6b44934ca6b&pm=s)
- When inserting a Mcleod integration into `integrations` table, you must specify the `tenant` field. For <PERSON><PERSON><PERSON>, the `tenant` should be the full host because it's also how you specify the environment.
  - For dev, `tenant` should be `{tenant}.loadtracking.com:5790`
  - For prod, `tenant` should be `{tenant}.loadtracking.com`
- In dev, you must be connected to Drumkit VPN in order to make requests to Trident's Mcleod. Follow instructions [here](https://www.notion.so/axleapi/Connect-to-Axle-VPN-d8735ba77c284572a7e9b1139eb4a923)

## Troubleshooting

- If you're getting a `401 Unauthorized` error, make sure you're using the correct `tenant` and that you're connected to Drumkit VPN.
- Token duration is unclear, so the code refreshes every 5 days. If even after refreshing the token, we're encountering 401s errors:
  - Basic authentication using username and password does not seem to work even when all the permissions are set correctly, so manually create a new token in the Sys Admin -> Smartphone -> Mobile Service menu in LoadMaster, aka the Mcleod app, in the remote Windows365 session (instructions adapted from [here](https://tryi.loadtracking.com/ws/docs/auth?role=-1)). Use the dev or prod Mcleod app depending on the environment you're in. If you're not seeing those menu options, then the `drumkit` account needs its permissions reconfigured. Reach out to Ryan Burney at Trident for help.
    ![img](./docs/CreateToken1.png)
    ![img](./docs/CreateToken2.png)
- If you're getting a `200` in some endpoints but not others, the user's permissions just need to be reconfigured. Reach out to Ryan Burney at Trident for help.
- When Trident syncs dev environment with prod, all of your changes in dev are overwritten. Syncs may also cause the drumkit account to be removed; reach out to Ryan Burney at Trident for help if that occurs.

- Note on timezones: Mcleod is timezone aware. It takes in any incoming timezone we give it and converts it to CST to display inside PowerBroker (the Mcleod TMS we support). 
  - This means if a location has a timezone in PST and we send it through as 08:00 am, it will display inside PowerBroker as 10:00am in the same location. 
  - Discovered during development with Tumalo's Mcleod